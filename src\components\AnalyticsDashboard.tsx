
import { <PERSON>, CardContent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  BarChart3,
  TrendingUp,
  Download,
  Filter,
  Brain,
  Lightbulb,
  Target,
  Calendar,
  MapPin,
  Users,
  Share2,
  Award,
  Zap
} from "lucide-react";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell, AreaChart, Area } from "recharts";

const AnalyticsDashboard = () => {
  const timeSeriesData = [
    { date: "Jul 1", referrals: 45, conversions: 12, revenue: 2400 },
    { date: "Jul 2", referrals: 52, conversions: 18, revenue: 3600 },
    { date: "Jul 3", referrals: 38, conversions: 15, revenue: 3000 },
    { date: "Jul 4", referrals: 64, conversions: 22, revenue: 4400 },
    { date: "Jul 5", referrals: 78, conversions: 28, revenue: 5600 },
    { date: "Jul 6", referrals: 89, conversions: 35, revenue: 7000 },
    { date: "Jul 7", referrals: 67, conversions: 24, revenue: 4800 },
    { date: "Jul 8", referrals: 95, conversions: 42, revenue: 8400 }
  ];

  const resortPerformance = [
    { resort: "Cabo Dreams", referrals: 145, conversions: 89, rate: 61 },
    { resort: "Riviera Paradise", referrals: 134, conversions: 78, rate: 58 },
    { resort: "Cancun Luxury", referrals: 128, conversions: 69, rate: 54 },
    { resort: "Playa Bonita", referrals: 112, conversions: 58, rate: 52 }
  ];

  const platformData = [
    { name: "WhatsApp", value: 45, color: "#25D366", conversions: 67 },
    { name: "SMS", value: 35, color: "#3B82F6", conversions: 52 },
    { name: "Email", value: 20, color: "#8B5CF6", conversions: 31 }
  ];

  const aiInsights = [
    {
      type: "opportunity",
      title: "Peak Performance Window",
      description: "Cabo guests show 23% higher conversion rates on Saturday evenings (6-8 PM). Consider targeted weekend campaigns.",
      confidence: 94,
      impact: "High",
      icon: Target
    },
    {
      type: "warning",
      title: "Engagement Drop Alert",
      description: "Riviera Paradise referrals declined 15% this week. Recent guests may need re-engagement campaign.",
      confidence: 87,
      impact: "Medium",
      icon: TrendingUp
    },
    {
      type: "suggestion",
      title: "WhatsApp Optimization",
      description: "Messages with emojis have 31% higher click rates. Template optimization could boost overall performance.",
      confidence: 92,
      impact: "Medium",
      icon: Lightbulb
    },
    {
      type: "prediction",
      title: "Summer Surge Forecast",
      description: "AI predicts 40% referral increase next week based on historical patterns and current bookings.",
      confidence: 78,
      impact: "High",
      icon: Brain
    }
  ];

  const topAgents = [
    { name: "Vikas Patel", resort: "Cabo Dreams", referrals: 156, conversion: 68, revenue: 12400 },
    { name: "Maria Rodriguez", resort: "Riviera Paradise", referrals: 134, conversion: 61, revenue: 10800 },
    { name: "Carlos Santos", resort: "Cancun Luxury", referrals: 128, conversion: 58, revenue: 9200 },
    { name: "Ana Martinez", resort: "Playa Bonita", referrals: 112, conversion: 52, revenue: 8600 }
  ];

  const getInsightIcon = (type) => {
    const icons = {
      opportunity: { icon: Target, color: "text-green-600", bg: "bg-green-100" },
      warning: { icon: TrendingUp, color: "text-orange-600", bg: "bg-orange-100" },
      suggestion: { icon: Lightbulb, color: "text-blue-600", bg: "bg-blue-100" },
      prediction: { icon: Brain, color: "text-purple-600", bg: "bg-purple-100" }
    };
    return icons[type] || icons.suggestion;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-slate-800">Analytics Dashboard</h2>
          <p className="text-slate-600 mt-1">AI-powered insights and performance metrics</p>
        </div>
        <div className="flex space-x-3">
          <Select defaultValue="7days">
            <SelectTrigger className="w-[150px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="24hours">Last 24 Hours</SelectItem>
              <SelectItem value="7days">Last 7 Days</SelectItem>
              <SelectItem value="30days">Last 30 Days</SelectItem>
              <SelectItem value="90days">Last 90 Days</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm">
            <Filter className="w-4 h-4 mr-2" />
            Filter
          </Button>
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* AI Insights Section */}
      <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Brain className="w-5 h-5 mr-2 text-purple-600" />
            AI Insights & Recommendations
          </CardTitle>
          <CardDescription>Automated analysis and actionable suggestions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {aiInsights.map((insight, index) => {
              const config = getInsightIcon(insight.type);
              const Icon = config.icon;
              
              return (
                <div key={index} className="p-4 bg-white rounded-lg border border-slate-200 hover:shadow-md transition-shadow">
                  <div className="flex items-start space-x-3">
                    <div className={`p-2 rounded-lg ${config.bg}`}>
                      <Icon className={`w-4 h-4 ${config.color}`} />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <h4 className="font-semibold text-slate-800">{insight.title}</h4>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className="text-xs">
                            {insight.confidence}% confidence
                          </Badge>
                          <Badge className={`text-xs ${
                            insight.impact === 'High' ? 'bg-red-100 text-red-700 border-red-200' :
                            insight.impact === 'Medium' ? 'bg-yellow-100 text-yellow-700 border-yellow-200' :
                            'bg-green-100 text-green-700 border-green-200'
                          }`}>
                            {insight.impact}
                          </Badge>
                        </div>
                      </div>
                      <p className="text-sm text-slate-600">{insight.description}</p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Performance Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="w-5 h-5 mr-2 text-blue-600" />
              Referral Trends
            </CardTitle>
            <CardDescription>Daily referrals and conversion performance</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={timeSeriesData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                <XAxis dataKey="date" stroke="#64748b" />
                <YAxis stroke="#64748b" />
                <Tooltip />
                <Area type="monotone" dataKey="referrals" stackId="1" stroke="#3b82f6" fill="#3b82f6" fillOpacity={0.3} />
                <Area type="monotone" dataKey="conversions" stackId="2" stroke="#10b981" fill="#10b981" fillOpacity={0.3} />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
          <CardHeader>
            <CardTitle className="flex items-center">
              <MapPin className="w-5 h-5 mr-2 text-green-600" />
              Resort Performance
            </CardTitle>
            <CardDescription>Conversion rates by resort location</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={resortPerformance}>
                <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                <XAxis dataKey="resort" stroke="#64748b" />
                <YAxis stroke="#64748b" />
                <Tooltip />
                <Bar dataKey="rate" fill="#10b981" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Platform & Revenue */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Share2 className="w-5 h-5 mr-2 text-purple-600" />
              Platform Performance
            </CardTitle>
            <CardDescription>Referral sharing by platform with conversion rates</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center mb-4">
              <ResponsiveContainer width="100%" height={200}>
                <PieChart>
                  <Pie
                    data={platformData}
                    cx="50%"
                    cy="50%"
                    innerRadius={40}
                    outerRadius={80}
                    dataKey="value"
                  >
                    {platformData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
            <div className="space-y-2">
              {platformData.map((platform, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full`} style={{ backgroundColor: platform.color }}></div>
                    <span className="text-sm text-slate-700">{platform.name}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-slate-800">{platform.value}% share</div>
                    <div className="text-xs text-slate-500">{platform.conversions}% conversion</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="w-5 h-5 mr-2 text-orange-600" />
              Revenue Impact
            </CardTitle>
            <CardDescription>Financial impact of referral program</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={250}>
              <LineChart data={timeSeriesData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                <XAxis dataKey="date" stroke="#64748b" />
                <YAxis stroke="#64748b" />
                <Tooltip formatter={(value) => [`$${value}`, 'Revenue']} />
                <Line type="monotone" dataKey="revenue" stroke="#f59e0b" strokeWidth={3} dot={{ fill: '#f59e0b' }} />
              </LineChart>
            </ResponsiveContainer>
            <div className="mt-4 p-3 bg-orange-50 rounded-lg border border-orange-200">
              <div className="text-sm text-orange-800">
                <strong>Total Revenue Impact:</strong> $41,200 generated through referral program
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Performers */}
      <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Award className="w-5 h-5 mr-2 text-yellow-600" />
            Top Performing Agents
          </CardTitle>
          <CardDescription>Leaderboard with AI-powered performance insights</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {topAgents.map((agent, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-gradient-to-r from-slate-50 to-blue-50 rounded-lg border border-slate-200">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-full font-bold">
                    #{index + 1}
                  </div>
                  <div>
                    <div className="font-semibold text-slate-800">{agent.name}</div>
                    <div className="text-sm text-slate-600 flex items-center">
                      <MapPin className="w-3 h-3 mr-1" />
                      {agent.resort}
                    </div>
                  </div>
                </div>
                <div className="grid grid-cols-3 gap-6 text-center">
                  <div>
                    <div className="font-semibold text-slate-800">{agent.referrals}</div>
                    <div className="text-xs text-slate-500">Referrals</div>
                  </div>
                  <div>
                    <div className="font-semibold text-green-600">{agent.conversion}%</div>
                    <div className="text-xs text-slate-500">Conversion</div>
                  </div>
                  <div>
                    <div className="font-semibold text-blue-600">${agent.revenue}</div>
                    <div className="text-xs text-slate-500">Revenue</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Predictive Analytics */}
      <Card className="bg-gradient-to-r from-blue-50 to-cyan-50 border-blue-200">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Zap className="w-5 h-5 mr-2 text-blue-600" />
            Predictive Campaign Suggestions
          </CardTitle>
          <CardDescription>AI-recommended actions for optimal performance</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-white rounded-lg border border-blue-200">
              <div className="flex items-center space-x-2 mb-2">
                <Target className="w-4 h-4 text-blue-600" />
                <span className="font-semibold text-slate-800">Best Time to Send</span>
              </div>
              <p className="text-sm text-slate-600">Saturday 6-8 PM shows highest engagement rates</p>
            </div>
            <div className="p-4 bg-white rounded-lg border border-blue-200">
              <div className="flex items-center space-x-2 mb-2">
                <Users className="w-4 h-4 text-green-600" />
                <span className="font-semibold text-slate-800">Target Audience</span>
              </div>
              <p className="text-sm text-slate-600">Focus on Cabo guests - 23% above average conversion</p>
            </div>
            <div className="p-4 bg-white rounded-lg border border-blue-200">
              <div className="flex items-center space-x-2 mb-2">
                <Lightbulb className="w-4 h-4 text-purple-600" />
                <span className="font-semibold text-slate-800">Content Optimization</span>
              </div>
              <p className="text-sm text-slate-600">Add more emojis - 31% higher click-through rates</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AnalyticsDashboard;
