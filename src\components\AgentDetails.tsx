import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  User,
  MapPin,
  Phone,
  Mail,
  Calendar,
  TrendingUp,
  TrendingDown,
  Users,
  Share2,
  CheckCircle,
  Clock,
  AlertTriangle,
  Eye,
  Target,
  Award,
  Activity,
  MessageSquare,
  Star,
  Search,
  Filter,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import { <PERSON>, Pie, Pie<PERSON>hart, ResponsiveContainer, Too<PERSON><PERSON> } from "recharts";

interface AgentDetailsProps {
  agentId: string;
  isOpen: boolean;
  onClose: () => void;
}

const AgentDetails = ({ agentId, isOpen, onClose }: AgentDetailsProps) => {
  const [selectedGuest, setSelectedGuest] = useState(null);
  const [selectedReferral, setSelectedReferral] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [expandedGuests, setExpandedGuests] = useState<Set<string>>(new Set());

  // Mock agent data
  const agentData = {
    id: agentId,
    name: "Vikas Patel",
    email: "<EMAIL>",
    phone: "******-0123",
    avatar: "/placeholder.svg",
    resort: "Cabo Dreams Resort",
    joinDate: "2024-01-15",
    status: "Active",
    tier: "Gold",
    totalReferrals: 156,
    completedReferrals: 106,
    completionRate: 68,
    monthlyTarget: 150,
    currentMonthReferrals: 89,
    totalEarnings: 15600,
    thisMonthEarnings: 4200,
    averageResponseTime: "2.3 hours",
    lastActivity: "2024-07-10",
    guestReferrals: [
      {
        guest: {
          id: "guest001",
          name: "John Smith",
          email: "<EMAIL>",
          phone: "******-0234",
          avatar: "/placeholder.svg",
          joinDate: "2024-07-05",
          status: "Active",
          totalReferrals: 3,
          completedReferrals: 1,
          acronym: "JS",
        },
        referrals: [
          {
            id: "ref001",
            name: "Sarah Johnson",
            email: "<EMAIL>",
            phone: "******-0456",
            status: "Completed",
            platform: "WhatsApp",
            sentDate: "2024-07-07",
            clickedDate: "2024-07-07",
            completedDate: "2024-07-08",
            responseTime: "2 hours",
            conversionValue: 0,
            acronym: "SJ",
          },
          {
            id: "ref002",
            name: "Mike Wilson",
            email: "<EMAIL>",
            phone: "******-0567",
            status: "Clicked",
            platform: "SMS",
            sentDate: "2024-07-08",
            clickedDate: "2024-07-08",
            responseTime: "4 hours",
            conversionValue: 0,
            acronym: "MW",
          },
          {
            id: "ref003",
            name: "Lisa Chen",
            email: "<EMAIL>",
            phone: "******-0678",
            status: "Sent",
            platform: "Email",
            sentDate: "2024-07-08",
            responseTime: "0 hours",
            conversionValue: 0,
            acronym: "LC",
          },
        ],
      },
      {
        guest: {
          id: "guest002",
          name: "Maria Garcia",
          email: "<EMAIL>",
          phone: "******-0789",
          avatar: "/placeholder.svg",
          joinDate: "2024-07-03",
          status: "Active",
          totalReferrals: 5,
          completedReferrals: 2,
          acronym: "MG",
        },
        referrals: [
          {
            id: "ref004",
            name: "Carlos Rivera",
            email: "<EMAIL>",
            phone: "******-0890",
            status: "Completed",
            platform: "WhatsApp",
            sentDate: "2024-07-06",
            clickedDate: "2024-07-06",
            completedDate: "2024-07-07",
            responseTime: "1 hour",
            conversionValue: 0,
            acronym: "CR",
          },
          {
            id: "ref005",
            name: "Emma Thompson",
            email: "<EMAIL>",
            phone: "******-0901",
            status: "Completed",
            platform: "WhatsApp",
            sentDate: "2024-07-07",
            clickedDate: "2024-07-07",
            completedDate: "2024-07-07",
            responseTime: "30 minutes",
            conversionValue: 0,
            acronym: "ET",
          },
          {
            id: "ref006",
            name: "David Brown",
            email: "<EMAIL>",
            phone: "******-1012",
            status: "Clicked",
            platform: "SMS",
            sentDate: "2024-07-07",
            clickedDate: "2024-07-07",
            responseTime: "6 hours",
            conversionValue: 0,
            acronym: "DB",
          },
          {
            id: "ref007",
            name: "Anna Wilson",
            email: "<EMAIL>",
            phone: "******-1123",
            status: "Failed",
            platform: "Email",
            sentDate: "2024-07-05",
            responseTime: "N/A",
            conversionValue: 0,
            acronym: "AW",
          },
          {
            id: "ref008",
            name: "Tom Davis",
            email: "<EMAIL>",
            phone: "******-1234",
            status: "Sent",
            platform: "WhatsApp",
            sentDate: "2024-07-08",
            responseTime: "0 hours",
            conversionValue: 0,
            acronym: "TD",
          },
        ],
      },
    ],
  };

  const getStatusColor = (status: string) => {
    const colors = {
      Completed: "bg-green-100 text-green-700 border-green-200",
      Clicked: "bg-blue-100 text-blue-700 border-blue-200",
      Sent: "bg-yellow-100 text-yellow-700 border-yellow-200",
      Failed: "bg-red-100 text-red-700 border-red-200",
      Active: "bg-green-100 text-green-700 border-green-200",
      Inactive: "bg-gray-100 text-gray-700 border-gray-200",
    };
    return colors[status] || colors["Sent"];
  };

  const getStatusIcon = (status: string) => {
    const icons = {
      Completed: CheckCircle,
      Clicked: Eye,
      Sent: Clock,
      Failed: AlertTriangle,
      Active: CheckCircle,
      Inactive: AlertTriangle,
    };
    return icons[status] || Clock;
  };

  const getTierColor = (tier: string) => {
    const colors = {
      Gold: "from-yellow-400 to-yellow-600",
      Silver: "from-gray-400 to-gray-600",
      Bronze: "from-orange-400 to-orange-600",
    };
    return colors[tier] || colors["Bronze"];
  };

  const toggleGuestExpansion = (guestId: string) => {
    const newExpanded = new Set(expandedGuests);
    if (newExpanded.has(guestId)) {
      newExpanded.delete(guestId);
    } else {
      newExpanded.add(guestId);
    }
    setExpandedGuests(newExpanded);
  };

  const filteredGuestReferrals = agentData.guestReferrals.filter(
    (guestData) =>
      guestData.guest.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      guestData.referrals.some((ref) =>
        ref.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
  );
  const generatePlatformData = [
    { name: "WhatsApp", value: 45, color: "#25D366" },
    { name: "SMS", value: 35, color: "#3B82F6" },
    { name: "Email", value: 20, color: "#8B5CF6" },
  ];
  return (
    <div className="space-y-8">
      {/* Sales RepresentativeOverview */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Profile Card */}
        <Card className="lg:col-span-1 bg-white/80 backdrop-blur-sm border-border/50 shadow-lg">
          <CardHeader className="text-center">
            <Avatar className="w-32 h-32 mx-auto mb-6 border-4 border-primary/20">
              <AvatarImage src={agentData.avatar} alt={agentData.name} />
              <AvatarFallback className="text-3xl bg-gradient-to-br from-primary to-secondary text-white">
                {agentData.name
                  .split(" ")
                  .map((n) => n[0])
                  .join("")}
              </AvatarFallback>
            </Avatar>
            <CardTitle className="text-2xl">{agentData.name}</CardTitle>
            <CardDescription className="text-base">
              ID: {agentData.id}
            </CardDescription>
            <Badge
              className={`mx-auto mt-3 bg-gradient-to-r ${getTierColor(
                agentData.tier
              )} text-white border-0 px-4 py-1`}
            >
              <Award className="w-4 h-4 mr-2" />
              {agentData.tier} Tier
            </Badge>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-3 text-base">
              <Mail className="w-5 h-5 text-muted-foreground" />
              <span>{agentData.email}</span>
            </div>
            <div className="flex items-center space-x-3 text-base">
              <Phone className="w-5 h-5 text-muted-foreground" />
              <span>{agentData.phone}</span>
            </div>
            <div className="flex items-center space-x-3 text-base">
              <MapPin className="w-5 h-5 text-muted-foreground" />
              <span>{agentData.resort}</span>
            </div>
            <div className="flex items-center space-x-3 text-base">
              <Calendar className="w-5 h-5 text-muted-foreground" />
              <span>Joined: {agentData.joinDate}</span>
            </div>
            <div className="flex items-center space-x-3 text-base">
              <Activity className="w-5 h-5 text-muted-foreground" />
              <span>Last Active: {agentData.lastActivity}</span>
            </div>
          </CardContent>
        </Card>

        {/* Performance Metrics */}
        <Card className="lg:col-span-2 bg-white/80 backdrop-blur-sm border-border/50 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center text-xl">
              <TrendingUp className="w-6 h-6 mr-3 text-primary" />
              Performance Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-6 mb-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-primary">
                  {agentData.totalReferrals}
                </div>
                <div className="text-sm text-muted-foreground font-medium">
                  Total Referrals
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">
                  {agentData.completedReferrals}
                </div>
                <div className="text-sm text-muted-foreground font-medium">
                  Completed
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-secondary">
                  {agentData.completionRate}%
                </div>
                <div className="text-sm text-muted-foreground font-medium">
                  Success Rate
                </div>
              </div>
              {/* <div className="text-center">
                <div className="text-3xl font-bold text-orange-600">${agentData.totalEarnings}</div>
                <div className="text-sm text-muted-foreground font-medium">Total Earnings</div>
              </div> */}
            </div>

            <div className="space-y-6">
              <div>
                <div className="flex justify-between items-center mb-3">
                  <span className="text-base font-semibold">
                    Monthly Target Progress
                  </span>
                  <span className="text-base text-muted-foreground font-medium">
                    {agentData.currentMonthReferrals} /{" "}
                    {agentData.monthlyTarget}
                  </span>
                </div>
                <Progress
                  value={
                    (agentData.currentMonthReferrals /
                      agentData.monthlyTarget) *
                    100
                  }
                  className="h-4"
                />
              </div>

              <div className="grid grid-cols-1 gap-6 text-base">
                {/* <div>
                  <span className="text-muted-foreground">This Month Earnings:</span>
                  <span className="font-bold ml-2 text-primary">${agentData.thisMonthEarnings}</span>
                </div> */}
                <div>
                  <span className="text-muted-foreground">
                    Avg Response Time:
                  </span>
                  <span className="font-bold ml-2 text-secondary">
                    {agentData.averageResponseTime}
                  </span>
                </div>
              </div>
            </div>
            <div className="flex justify-between mt-6">
              <div className="grid grid-cols-1 md:grid-cols-1 gap-6">
                <Card className="bg-gradient-to-br from-green-50 flex  to-green-100 border-green-200">
                  <CardContent className="p-6 text-center ">
                    <div className="text-3xl font-bold text-green-600">
                      {agentData.guestReferrals.reduce(
                        (acc, guest) =>
                          acc +
                          guest.referrals.filter(
                            (r) => r.status === "Completed"
                          ).length,
                        0
                      )}
                    </div>
                    <div className="text-sm text-green-700 font-medium">
                      Completed Referrals
                    </div>
                  </CardContent>
                </Card>
                <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
                  <CardContent className="p-6 text-center">
                    <div className="text-3xl font-bold text-blue-600">
                      {agentData.guestReferrals.reduce(
                        (acc, guest) =>
                          acc +
                          guest.referrals.filter((r) => r.status === "Clicked")
                            .length,
                        0
                      )}
                    </div>
                    <div className="text-sm text-blue-700 font-medium">
                      Clicked Referrals
                    </div>
                  </CardContent>
                </Card>
                <Card className="bg-gradient-to-br from-yellow-50 to-yellow-100 border-yellow-200">
                  <CardContent className="p-6 text-center">
                    <div className="text-3xl font-bold text-yellow-600">
                      {agentData.guestReferrals.reduce(
                        (acc, guest) =>
                          acc +
                          guest.referrals.filter((r) => r.status === "Sent")
                            .length,
                        0
                      )}
                    </div>
                    <div className="text-sm text-yellow-700 font-medium">
                      Pending Referrals
                    </div>
                  </CardContent>
                </Card>
              </div>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={generatePlatformData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={120}
                    dataKey="value"
                    label={({ name, value }) => `${name} ${value}%`}
                  >
                    {generatePlatformData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Referral Hierarchy */}
      <Card className="bg-white/80 backdrop-blur-sm border-border/50 shadow-lg">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center text-xl">
                <Share2 className="w-6 h-6 mr-3 text-primary" />
                Referral Hierarchy & Chain
              </CardTitle>
              <CardDescription className="text-base">
                Complete view of guest referrals and their conversion states
              </CardDescription>
            </div>
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <Input
                  placeholder="Search guests or referrals..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>
              <Button variant="outline" size="sm">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="hierarchy" className="w-full">
            <TabsList className="grid w-full grid-cols-1">
              <TabsTrigger value="hierarchy">Hierarchy View</TabsTrigger>
              {/* <TabsTrigger value="analytics">Analytics View</TabsTrigger> */}
            </TabsList>

            <TabsContent value="hierarchy" className="space-y-6 mt-6">
              {filteredGuestReferrals.map((guestData, guestIndex) => {
                const isExpanded = expandedGuests.has(guestData.guest.id);
                return (
                  <div
                    key={guestIndex}
                    className="border rounded-xl overflow-hidden bg-gradient-to-r from-muted/20 to-muted/10"
                  >
                    <Collapsible>
                      <CollapsibleTrigger
                        className="w-full"
                        onClick={() => toggleGuestExpansion(guestData.guest.id)}
                      >
                        <div className="flex items-center justify-between p-6 hover:bg-muted/30 transition-colors">
                          <div className="flex items-center space-x-4">
                            <div className="flex items-center space-x-3">
                              {isExpanded ? (
                                <ChevronDown className="w-5 h-5 text-muted-foreground" />
                              ) : (
                                <ChevronRight className="w-5 h-5 text-muted-foreground" />
                              )}
                              <div className="w-12 h-12 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg">
                                {guestData.guest.acronym}
                              </div>
                            </div>
                            <div className="text-left">
                              <div className="font-bold text-foreground text-lg">
                                {guestData.guest.name}
                              </div>
                              <div className="text-sm text-muted-foreground flex items-center space-x-4">
                                <span className="flex items-center space-x-1">
                                  <Mail className="w-3 h-3" />
                                  <span>{guestData.guest.email}</span>
                                </span>
                                <span className="flex items-center space-x-1">
                                  <Phone className="w-3 h-3" />
                                  <span>{guestData.guest.phone}</span>
                                </span>
                              </div>
                              <div className="text-xs text-muted-foreground mt-1">
                                Joined: {guestData.guest.joinDate} • Referrals:{" "}
                                {guestData.guest.totalReferrals} • Completed:{" "}
                                {guestData.guest.completedReferrals}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-3">
                            <Badge
                              className={`${getStatusColor(
                                guestData.guest.status
                              )} font-medium`}
                            >
                              {guestData.guest.status}
                            </Badge>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                setSelectedGuest(guestData.guest);
                              }}
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </CollapsibleTrigger>

                      <CollapsibleContent>
                        <div className="px-6 pb-6">
                          <div className="ml-16 space-y-3">
                            {guestData.referrals.map((referral, refIndex) => {
                              const StatusIcon = getStatusIcon(referral.status);
                              return (
                                <div
                                  key={refIndex}
                                  className="flex items-center justify-between p-4 bg-white/60 rounded-lg border border-border/30 hover:bg-white/80 transition-colors"
                                >
                                  <div className="flex items-center space-x-4">
                                    <div className="flex items-center space-x-3">
                                      <StatusIcon className="w-5 h-5 text-muted-foreground" />
                                      <div className="w-2 h-px bg-border"></div>
                                      <div className="w-10 h-10 bg-gradient-to-br from-secondary to-primary rounded-full flex items-center justify-center text-white font-semibold text-sm shadow-md">
                                        {referral.acronym}
                                      </div>
                                    </div>
                                    <div className="flex-1">
                                      <div className="font-semibold text-foreground text-base">
                                        {referral.name}
                                      </div>
                                      <div className="text-sm text-muted-foreground flex items-center space-x-4 mt-1">
                                        <span className="flex items-center space-x-1">
                                          <MessageSquare className="w-3 h-3" />
                                          <span>{referral.platform}</span>
                                        </span>
                                        <span className="flex items-center space-x-1">
                                          <Calendar className="w-3 h-3" />
                                          <span>{referral.sentDate}</span>
                                        </span>
                                        <span className="flex items-center space-x-1">
                                          <Clock className="w-3 h-3" />
                                          <span>
                                            Response: {referral.responseTime}
                                          </span>
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="flex items-center space-x-4">
                                    <div className="text-right">
                                      <Badge
                                        className={`${getStatusColor(
                                          referral.status
                                        )} font-medium`}
                                      >
                                        {referral.status}
                                      </Badge>
                                      {referral.conversionValue > 0 && (
                                        <div className="text-sm font-bold text-green-600 mt-1">
                                          ${referral.conversionValue}
                                        </div>
                                      )}
                                    </div>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() =>
                                        setSelectedReferral(referral)
                                      }
                                    >
                                      <Eye className="w-4 h-4" />
                                    </Button>
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      </CollapsibleContent>
                    </Collapsible>
                  </div>
                );
              })}
            </TabsContent>

            <TabsContent value="analytics" className="space-y-6 mt-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
                  <CardContent className="p-6 text-center">
                    <div className="text-3xl font-bold text-green-600">
                      {agentData.guestReferrals.reduce(
                        (acc, guest) =>
                          acc +
                          guest.referrals.filter(
                            (r) => r.status === "Completed"
                          ).length,
                        0
                      )}
                    </div>
                    <div className="text-sm text-green-700 font-medium">
                      Completed Referrals
                    </div>
                  </CardContent>
                </Card>
                <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
                  <CardContent className="p-6 text-center">
                    <div className="text-3xl font-bold text-blue-600">
                      {agentData.guestReferrals.reduce(
                        (acc, guest) =>
                          acc +
                          guest.referrals.filter((r) => r.status === "Clicked")
                            .length,
                        0
                      )}
                    </div>
                    <div className="text-sm text-blue-700 font-medium">
                      Clicked Referrals
                    </div>
                  </CardContent>
                </Card>
                <Card className="bg-gradient-to-br from-yellow-50 to-yellow-100 border-yellow-200">
                  <CardContent className="p-6 text-center">
                    <div className="text-3xl font-bold text-yellow-600">
                      {agentData.guestReferrals.reduce(
                        (acc, guest) =>
                          acc +
                          guest.referrals.filter((r) => r.status === "Sent")
                            .length,
                        0
                      )}
                    </div>
                    <div className="text-sm text-yellow-700 font-medium">
                      Pending Referrals
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card className="bg-white/60 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-lg">
                    Platform Performance
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {["WhatsApp", "SMS", "Email"].map((platform) => {
                      const platformReferrals = agentData.guestReferrals
                        .flatMap((g) => g.referrals)
                        .filter((r) => r.platform === platform);
                      const completed = platformReferrals.filter(
                        (r) => r.status === "Completed"
                      ).length;
                      const total = platformReferrals.length;
                      const rate =
                        total > 0 ? Math.round((completed / total) * 100) : 0;

                      return (
                        <div
                          key={platform}
                          className="flex items-center justify-between p-4 bg-white/80 rounded-lg"
                        >
                          <div className="flex items-center space-x-4">
                            <MessageSquare className="w-5 h-5 text-muted-foreground" />
                            <span className="font-semibold text-base">
                              {platform}
                            </span>
                          </div>
                          <div className="flex items-center space-x-6">
                            <span className="text-sm text-muted-foreground font-medium">
                              {completed}/{total} ({rate}%)
                            </span>
                            <Progress value={rate} className="w-24 h-3" />
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Guest Detail Modal */}
      <Dialog
        open={!!selectedGuest}
        onOpenChange={() => setSelectedGuest(null)}
      >
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Guest Details</DialogTitle>
          </DialogHeader>
          {selectedGuest && (
            <div className="space-y-6">
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center text-white font-bold text-xl">
                  {selectedGuest.name
                    .split(" ")
                    .map((n) => n[0])
                    .join("")}
                </div>
                <div>
                  <div className="font-bold text-lg">{selectedGuest.name}</div>
                  <div className="text-sm text-muted-foreground">
                    {selectedGuest.email}
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="text-sm text-muted-foreground font-medium">
                    Phone:
                  </span>
                  <div className="font-semibold">{selectedGuest.phone}</div>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground font-medium">
                    Status:
                  </span>
                  <div>
                    <Badge className={getStatusColor(selectedGuest.status)}>
                      {selectedGuest.status}
                    </Badge>
                  </div>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground font-medium">
                    Join Date:
                  </span>
                  <div className="font-semibold">{selectedGuest.joinDate}</div>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground font-medium">
                    Referrals:
                  </span>
                  <div className="font-semibold">
                    {selectedGuest.totalReferrals}
                  </div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Referral Detail Modal */}
      <Dialog
        open={!!selectedReferral}
        onOpenChange={() => setSelectedReferral(null)}
      >
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Referral Details</DialogTitle>
          </DialogHeader>
          {selectedReferral && (
            <div className="space-y-6">
              <div>
                <div className="font-bold text-xl">{selectedReferral.name}</div>
                <div className="text-sm text-muted-foreground">
                  {selectedReferral.email}
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="text-sm text-muted-foreground font-medium">
                    Phone:
                  </span>
                  <div className="font-semibold">{selectedReferral.phone}</div>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground font-medium">
                    Platform:
                  </span>
                  <div className="font-semibold">
                    {selectedReferral.platform}
                  </div>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground font-medium">
                    Status:
                  </span>
                  <div>
                    <Badge className={getStatusColor(selectedReferral.status)}>
                      {selectedReferral.status}
                    </Badge>
                  </div>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground font-medium">
                    Sent Date:
                  </span>
                  <div className="font-semibold">
                    {selectedReferral.sentDate}
                  </div>
                </div>
                {selectedReferral.clickedDate && (
                  <div>
                    <span className="text-sm text-muted-foreground font-medium">
                      Clicked:
                    </span>
                    <div className="font-semibold">
                      {selectedReferral.clickedDate}
                    </div>
                  </div>
                )}
                {selectedReferral.completedDate && (
                  <div>
                    <span className="text-sm text-muted-foreground font-medium">
                      Completed:
                    </span>
                    <div className="font-semibold">
                      {selectedReferral.completedDate}
                    </div>
                  </div>
                )}
                <div>
                  <span className="text-sm text-muted-foreground font-medium">
                    Response Time:
                  </span>
                  <div className="font-semibold">
                    {selectedReferral.responseTime}
                  </div>
                </div>
                {selectedReferral.conversionValue > 0 && (
                  <div>
                    <span className="text-sm text-muted-foreground font-medium">
                      Value:
                    </span>
                    <div className="font-bold text-green-600">
                      ${selectedReferral.conversionValue}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AgentDetails;
