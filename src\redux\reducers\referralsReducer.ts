import { ReferralsState, Action } from '../types';
import * as actionTypes from '../constants/actionTypes';

const initialState: ReferralsState = {
  referrals: [],
  loading: false,
  error: null
};

const referralsReducer = (state = initialState, action: Action): ReferralsState => {
  switch (action.type) {
    case actionTypes.FETCH_REFERRALS_REQUEST:
    case actionTypes.ADD_REFERRAL_REQUEST:
    case actionTypes.UPDATE_REFERRAL_REQUEST:
    case actionTypes.DELETE_REFERRAL_REQUEST:
      return {
        ...state,
        loading: true,
        error: null
      };

    case actionTypes.FETCH_REFERRALS_SUCCESS:
      return {
        ...state,
        loading: false,
        referrals: action.payload,
        error: null
      };

    case actionTypes.ADD_REFERRAL_SUCCESS:
      return {
        ...state,
        loading: false,
        referrals: [...state.referrals, action.payload],
        error: null
      };

    case actionTypes.UPDATE_REFERRAL_SUCCESS:
      return {
        ...state,
        loading: false,
        referrals: state.referrals.map(referral =>
          referral.id === action.payload.id ? action.payload : referral
        ),
        error: null
      };

    case actionTypes.DELETE_REFERRAL_SUCCESS:
      return {
        ...state,
        loading: false,
        referrals: state.referrals.filter(referral => referral.id !== action.payload),
        error: null
      };

    case actionTypes.FETCH_REFERRALS_FAILURE:
    case actionTypes.ADD_REFERRAL_FAILURE:
    case actionTypes.UPDATE_REFERRAL_FAILURE:
    case actionTypes.DELETE_REFERRAL_FAILURE:
      return {
        ...state,
        loading: false,
        error: action.payload
      };

    default:
      return state;
  }
};

export default referralsReducer;
