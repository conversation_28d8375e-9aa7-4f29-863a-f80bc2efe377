import { DashboardState, Action } from '../types';
import * as actionTypes from '../constants/actionTypes';

const initialState: DashboardState = {
  stats: null,
  chartData: [],
  filters: {
    period: 'daily',
    dateRange: {
      from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      to: new Date()
    }
  },
  loading: false,
  error: null
};

const dashboardReducer = (state = initialState, action: Action): DashboardState => {
  switch (action.type) {
    case actionTypes.FETCH_DASHBOARD_DATA_REQUEST:
      return {
        ...state,
        loading: true,
        error: null
      };

    case actionTypes.FETCH_DASHBOARD_DATA_SUCCESS:
      return {
        ...state,
        loading: false,
        stats: action.payload.stats,
        chartData: action.payload.chartData,
        error: null
      };

    case actionTypes.FETCH_DASHBOARD_DATA_FAILURE:
      return {
        ...state,
        loading: false,
        error: action.payload
      };

    case actionTypes.UPDATE_DASHBOARD_FILTERS:
      return {
        ...state,
        filters: action.payload
      };

    default:
      return state;
  }
};

export default dashboardReducer;
