// Dashboard Constants
export const FETCH_DASHBOARD_DATA_REQUEST = 'FETCH_DASHBOARD_DATA_REQUEST';
export const FETCH_DASHBOARD_DATA_SUCCESS = 'FETCH_DASHBOARD_DATA_SUCCESS';
export const FETCH_DASHBOARD_DATA_FAILURE = 'FETCH_DASHBOARD_DATA_FAILURE';
export const UPDATE_DASHBOARD_FILTERS = 'UPDATE_DASHBOARD_FILTERS';

// Agents Constants
export const FETCH_AGENTS_REQUEST = 'FETCH_AGENTS_REQUEST';
export const FETCH_AGENTS_SUCCESS = 'FETCH_AGENTS_SUCCESS';
export const FETCH_AGENTS_FAILURE = 'FETCH_AGENTS_FAILURE';
export const ADD_AGENT_REQUEST = 'ADD_AGENT_REQUEST';
export const ADD_AGENT_SUCCESS = 'ADD_AGENT_SUCCESS';
export const ADD_AGENT_FAILURE = 'ADD_AGENT_FAILURE';
export const UPDATE_AGENT_REQUEST = 'UPDATE_AGENT_REQUEST';
export const UPDATE_AGENT_SUCCESS = 'UPDATE_AGENT_SUCCESS';
export const UPDATE_AGENT_FAILURE = 'UPDATE_AGENT_FAILURE';
export const DELETE_AGENT_REQUEST = 'DELETE_AGENT_REQUEST';
export const DELETE_AGENT_SUCCESS = 'DELETE_AGENT_SUCCESS';
export const DELETE_AGENT_FAILURE = 'DELETE_AGENT_FAILURE';
export const SET_SELECTED_AGENT = 'SET_SELECTED_AGENT';

// Guests Constants
export const FETCH_GUESTS_REQUEST = 'FETCH_GUESTS_REQUEST';
export const FETCH_GUESTS_SUCCESS = 'FETCH_GUESTS_SUCCESS';
export const FETCH_GUESTS_FAILURE = 'FETCH_GUESTS_FAILURE';
export const ADD_GUEST_REQUEST = 'ADD_GUEST_REQUEST';
export const ADD_GUEST_SUCCESS = 'ADD_GUEST_SUCCESS';
export const ADD_GUEST_FAILURE = 'ADD_GUEST_FAILURE';
export const UPDATE_GUEST_REQUEST = 'UPDATE_GUEST_REQUEST';
export const UPDATE_GUEST_SUCCESS = 'UPDATE_GUEST_SUCCESS';
export const UPDATE_GUEST_FAILURE = 'UPDATE_GUEST_FAILURE';
export const DELETE_GUEST_REQUEST = 'DELETE_GUEST_REQUEST';
export const DELETE_GUEST_SUCCESS = 'DELETE_GUEST_SUCCESS';
export const DELETE_GUEST_FAILURE = 'DELETE_GUEST_FAILURE';

// Referrals Constants
export const FETCH_REFERRALS_REQUEST = 'FETCH_REFERRALS_REQUEST';
export const FETCH_REFERRALS_SUCCESS = 'FETCH_REFERRALS_SUCCESS';
export const FETCH_REFERRALS_FAILURE = 'FETCH_REFERRALS_FAILURE';
export const ADD_REFERRAL_REQUEST = 'ADD_REFERRAL_REQUEST';
export const ADD_REFERRAL_SUCCESS = 'ADD_REFERRAL_SUCCESS';
export const ADD_REFERRAL_FAILURE = 'ADD_REFERRAL_FAILURE';
export const UPDATE_REFERRAL_REQUEST = 'UPDATE_REFERRAL_REQUEST';
export const UPDATE_REFERRAL_SUCCESS = 'UPDATE_REFERRAL_SUCCESS';
export const UPDATE_REFERRAL_FAILURE = 'UPDATE_REFERRAL_FAILURE';
export const DELETE_REFERRAL_REQUEST = 'DELETE_REFERRAL_REQUEST';
export const DELETE_REFERRAL_SUCCESS = 'DELETE_REFERRAL_SUCCESS';
export const DELETE_REFERRAL_FAILURE = 'DELETE_REFERRAL_FAILURE';

// Contacts Constants
export const FETCH_CONTACTS_REQUEST = 'FETCH_CONTACTS_REQUEST';
export const FETCH_CONTACTS_SUCCESS = 'FETCH_CONTACTS_SUCCESS';
export const FETCH_CONTACTS_FAILURE = 'FETCH_CONTACTS_FAILURE';
export const ADD_CONTACT_REQUEST = 'ADD_CONTACT_REQUEST';
export const ADD_CONTACT_SUCCESS = 'ADD_CONTACT_SUCCESS';
export const ADD_CONTACT_FAILURE = 'ADD_CONTACT_FAILURE';
export const UPDATE_CONTACT_REQUEST = 'UPDATE_CONTACT_REQUEST';
export const UPDATE_CONTACT_SUCCESS = 'UPDATE_CONTACT_SUCCESS';
export const UPDATE_CONTACT_FAILURE = 'UPDATE_CONTACT_FAILURE';
export const DELETE_CONTACT_REQUEST = 'DELETE_CONTACT_REQUEST';
export const DELETE_CONTACT_SUCCESS = 'DELETE_CONTACT_SUCCESS';
export const DELETE_CONTACT_FAILURE = 'DELETE_CONTACT_FAILURE';

// Notifications Constants
export const FETCH_NOTIFICATIONS_REQUEST = 'FETCH_NOTIFICATIONS_REQUEST';
export const FETCH_NOTIFICATIONS_SUCCESS = 'FETCH_NOTIFICATIONS_SUCCESS';
export const FETCH_NOTIFICATIONS_FAILURE = 'FETCH_NOTIFICATIONS_FAILURE';
export const ADD_NOTIFICATION_REQUEST = 'ADD_NOTIFICATION_REQUEST';
export const ADD_NOTIFICATION_SUCCESS = 'ADD_NOTIFICATION_SUCCESS';
export const ADD_NOTIFICATION_FAILURE = 'ADD_NOTIFICATION_FAILURE';
export const UPDATE_NOTIFICATION_REQUEST = 'UPDATE_NOTIFICATION_REQUEST';
export const UPDATE_NOTIFICATION_SUCCESS = 'UPDATE_NOTIFICATION_SUCCESS';
export const UPDATE_NOTIFICATION_FAILURE = 'UPDATE_NOTIFICATION_FAILURE';
export const DELETE_NOTIFICATION_REQUEST = 'DELETE_NOTIFICATION_REQUEST';
export const DELETE_NOTIFICATION_SUCCESS = 'DELETE_NOTIFICATION_SUCCESS';
export const DELETE_NOTIFICATION_FAILURE = 'DELETE_NOTIFICATION_FAILURE';
export const MARK_NOTIFICATION_READ = 'MARK_NOTIFICATION_READ';

// Rewards Constants
export const FETCH_REWARDS_REQUEST = 'FETCH_REWARDS_REQUEST';
export const FETCH_REWARDS_SUCCESS = 'FETCH_REWARDS_SUCCESS';
export const FETCH_REWARDS_FAILURE = 'FETCH_REWARDS_FAILURE';
export const ADD_REWARD_REQUEST = 'ADD_REWARD_REQUEST';
export const ADD_REWARD_SUCCESS = 'ADD_REWARD_SUCCESS';
export const ADD_REWARD_FAILURE = 'ADD_REWARD_FAILURE';
export const UPDATE_REWARD_REQUEST = 'UPDATE_REWARD_REQUEST';
export const UPDATE_REWARD_SUCCESS = 'UPDATE_REWARD_SUCCESS';
export const UPDATE_REWARD_FAILURE = 'UPDATE_REWARD_FAILURE';
export const DELETE_REWARD_REQUEST = 'DELETE_REWARD_REQUEST';
export const DELETE_REWARD_SUCCESS = 'DELETE_REWARD_SUCCESS';
export const DELETE_REWARD_FAILURE = 'DELETE_REWARD_FAILURE';
export const CLAIM_REWARD_REQUEST = 'CLAIM_REWARD_REQUEST';
export const CLAIM_REWARD_SUCCESS = 'CLAIM_REWARD_SUCCESS';
export const CLAIM_REWARD_FAILURE = 'CLAIM_REWARD_FAILURE';

// Analytics Constants
export const FETCH_ANALYTICS_REQUEST = 'FETCH_ANALYTICS_REQUEST';
export const FETCH_ANALYTICS_SUCCESS = 'FETCH_ANALYTICS_SUCCESS';
export const FETCH_ANALYTICS_FAILURE = 'FETCH_ANALYTICS_FAILURE';
export const UPDATE_ANALYTICS_FILTERS = 'UPDATE_ANALYTICS_FILTERS';

// Referral Tree Constants
export const FETCH_REFERRAL_TREE_REQUEST = 'FETCH_REFERRAL_TREE_REQUEST';
export const FETCH_REFERRAL_TREE_SUCCESS = 'FETCH_REFERRAL_TREE_SUCCESS';
export const FETCH_REFERRAL_TREE_FAILURE = 'FETCH_REFERRAL_TREE_FAILURE';
export const EXPAND_TREE_NODE = 'EXPAND_TREE_NODE';
export const COLLAPSE_TREE_NODE = 'COLLAPSE_TREE_NODE';

// Integrations Constants
export const FETCH_INTEGRATIONS_REQUEST = 'FETCH_INTEGRATIONS_REQUEST';
export const FETCH_INTEGRATIONS_SUCCESS = 'FETCH_INTEGRATIONS_SUCCESS';
export const FETCH_INTEGRATIONS_FAILURE = 'FETCH_INTEGRATIONS_FAILURE';
export const UPDATE_INTEGRATION_REQUEST = 'UPDATE_INTEGRATION_REQUEST';
export const UPDATE_INTEGRATION_SUCCESS = 'UPDATE_INTEGRATION_SUCCESS';
export const UPDATE_INTEGRATION_FAILURE = 'UPDATE_INTEGRATION_FAILURE';
export const TEST_INTEGRATION_REQUEST = 'TEST_INTEGRATION_REQUEST';
export const TEST_INTEGRATION_SUCCESS = 'TEST_INTEGRATION_SUCCESS';
export const TEST_INTEGRATION_FAILURE = 'TEST_INTEGRATION_FAILURE';

// User Profile Constants
export const FETCH_USER_PROFILE_REQUEST = 'FETCH_USER_PROFILE_REQUEST';
export const FETCH_USER_PROFILE_SUCCESS = 'FETCH_USER_PROFILE_SUCCESS';
export const FETCH_USER_PROFILE_FAILURE = 'FETCH_USER_PROFILE_FAILURE';
export const UPDATE_USER_PROFILE_REQUEST = 'UPDATE_USER_PROFILE_REQUEST';
export const UPDATE_USER_PROFILE_SUCCESS = 'UPDATE_USER_PROFILE_SUCCESS';
export const UPDATE_USER_PROFILE_FAILURE = 'UPDATE_USER_PROFILE_FAILURE';
export const UPLOAD_AVATAR_REQUEST = 'UPLOAD_AVATAR_REQUEST';
export const UPLOAD_AVATAR_SUCCESS = 'UPLOAD_AVATAR_SUCCESS';
export const UPLOAD_AVATAR_FAILURE = 'UPLOAD_AVATAR_FAILURE';

// UI Constants
export const SET_LOADING = 'SET_LOADING';
export const SET_ERROR = 'SET_ERROR';
export const CLEAR_ERROR = 'CLEAR_ERROR';
export const SET_ACTIVE_TAB = 'SET_ACTIVE_TAB';
export const TOGGLE_SIDEBAR = 'TOGGLE_SIDEBAR';
