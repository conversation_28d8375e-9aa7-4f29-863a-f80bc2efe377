
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Bell,
  Plus,
  Send,
  Clock,
  Users,
  Target,
  Calendar,
  Edit,
  Play,
  Pause,
  Trash2
} from "lucide-react";
import { toast } from "sonner";

const PushNotifications = () => {
  const [isCreateOpen, setIsCreateOpen] = useState(false);
  const [selectedCampaign, setSelectedCampaign] = useState(null);

  const campaigns = [
    {
      id: 1,
      title: "🔥 Double Rewards Weekend!",
      message: "Get 2x rewards for every referral this weekend. Share now!",
      target: "All Users",
      targetCount: 1247,
      status: "Sent",
      sentDate: "2024-07-08",
      openRate: "68%",
      clickRate: "24%"
    },
    {
      id: 2,
      title: "🎉 New Badge Unlocked!",
      message: "You're on fire! You've earned the 'Social Butterfly' badge.",
      target: "Active Referrers",
      targetCount: 156,
      status: "Scheduled",
      sentDate: "2024-07-09",
      openRate: "-",
      clickRate: "-"
    },
    {
      id: 3,
      title: "💎 Exclusive Cabo Offer",
      message: "Limited time: 25% off your next Cabo Dreams booking!",
      target: "Cabo Guests",
      targetCount: 89,
      status: "Draft",
      sentDate: "-",
      openRate: "-",
      clickRate: "-"
    }
  ];

  const templates = [
    { id: 1, title: "🔥 Double Rewards", message: "Get 2x rewards for every referral this weekend!" },
    { id: 2, title: "🎉 New Badge", message: "Congratulations! You've unlocked a new achievement badge." },
    { id: 3, title: "💎 Exclusive Offer", message: "Limited time offer just for you!" },
    { id: 4, title: "⏰ Reminder", message: "Don't forget to share your experience with friends!" },
    { id: 5, title: "🏆 Streak Alert", message: "You're on a roll! Keep your referral streak going." }
  ];

  const targetOptions = [
    { value: "all", label: "All Users", count: 1247 },
    { value: "active", label: "Active Users", count: 892 },
    { value: "inactive", label: "Inactive Users (7+ days)", count: 355 },
    { value: "cabo", label: "Cabo Guests", count: 89 },
    { value: "riviera", label: "Riviera Guests", count: 156 },
    { value: "cancun", label: "Cancun Guests", count: 78 },
    { value: "pending_referrals", label: "Pending Referrals (24h+)", count: 203 }
  ];

  const getStatusBadge = (status) => {
    const statusConfig = {
      "Sent": { color: "bg-green-100 text-green-700 border-green-200" },
      "Scheduled": { color: "bg-blue-100 text-blue-700 border-blue-200" },
      "Draft": { color: "bg-gray-100 text-gray-700 border-gray-200" },
      "Failed": { color: "bg-red-100 text-red-700 border-red-200" }
    };
    return statusConfig[status] || statusConfig["Draft"];
  };

  const handleCreateCampaign = () => {
    toast.success("Push notification campaign created successfully!");
    setIsCreateOpen(false);
  };

  const handleSendNow = (campaignId) => {
    toast.success("Push notification sent to all targeted users!");
  };

  const handleSchedule = (campaignId) => {
    toast.success("Push notification scheduled successfully!");
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-slate-800">Push Notifications</h2>
          <p className="text-slate-600 mt-1">Engage users with targeted push campaigns</p>
        </div>
        <Button onClick={() => setIsCreateOpen(true)} className="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600">
          <Plus className="w-4 h-4 mr-2" />
          Create Campaign
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Total Campaigns</p>
                <p className="text-2xl font-bold text-slate-800">{campaigns.length}</p>
              </div>
              <Bell className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Sent Today</p>
                <p className="text-2xl font-bold text-slate-800">
                  {campaigns.filter(c => c.status === 'Sent').length}
                </p>
              </div>
              <Send className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Avg Open Rate</p>
                <p className="text-2xl font-bold text-slate-800">68%</p>
              </div>
              <Target className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Total Reach</p>
                <p className="text-2xl font-bold text-slate-800">1,247</p>
              </div>
              <Users className="w-8 h-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Campaigns Table */}
      <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
        <CardHeader>
          <CardTitle>Campaign History</CardTitle>
          <CardDescription>Manage and monitor push notification campaigns</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Campaign</TableHead>
                <TableHead>Target</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Performance</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {campaigns.map((campaign) => {
                const statusConfig = getStatusBadge(campaign.status);
                
                return (
                  <TableRow key={campaign.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium text-slate-800">{campaign.title}</div>
                        <div className="text-sm text-slate-600 max-w-xs truncate">
                          {campaign.message}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium text-slate-800">{campaign.target}</div>
                        <div className="text-sm text-slate-600">{campaign.targetCount} users</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={statusConfig.color}>
                        {campaign.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="text-sm text-slate-700">Open: {campaign.openRate}</div>
                        <div className="text-sm text-slate-700">Click: {campaign.clickRate}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm text-slate-600 flex items-center">
                        <Calendar className="w-3 h-3 mr-1" />
                        {campaign.sentDate}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        {campaign.status === "Draft" && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleSendNow(campaign.id)}
                          >
                            <Send className="w-3 h-3" />
                          </Button>
                        )}
                        {campaign.status === "Scheduled" && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleSchedule(campaign.id)}
                          >
                            <Pause className="w-3 h-3" />
                          </Button>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                        >
                          <Edit className="w-3 h-3" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Create Campaign Dialog */}
      <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create Push Campaign</DialogTitle>
            <DialogDescription>Design and target your push notification</DialogDescription>
          </DialogHeader>
          
          <div className="space-y-6">
            <div>
              <Label htmlFor="title">Campaign Title</Label>
              <Input id="title" placeholder="🔥 Amazing offer inside!" />
            </div>

            <div>
              <Label htmlFor="message">Message</Label>
              <Textarea 
                id="message" 
                placeholder="Your exciting message here..."
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="target">Target Audience</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select target audience" />
                </SelectTrigger>
                <SelectContent>
                  {targetOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label} ({option.count} users)
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className="text-base font-semibold">Quick Templates</Label>
              <div className="grid grid-cols-2 gap-2 mt-2">
                {templates.map((template) => (
                  <Button
                    key={template.id}
                    variant="outline"
                    size="sm"
                    className="text-left justify-start h-auto p-3"
                    onClick={() => {
                      // Auto-fill form with template
                      toast.success("Template applied!");
                    }}
                  >
                    <div>
                      <div className="font-medium">{template.title}</div>
                      <div className="text-xs text-slate-500 truncate">
                        {template.message}
                      </div>
                    </div>
                  </Button>
                ))}
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-6 border-t">
              <Button variant="outline" onClick={() => setIsCreateOpen(false)}>
                Save Draft
              </Button>
              <Button variant="outline">
                <Clock className="w-4 h-4 mr-2" />
                Schedule
              </Button>
              <Button onClick={handleCreateCampaign} className="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600">
                <Send className="w-4 h-4 mr-2" />
                Send Now
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PushNotifications;
