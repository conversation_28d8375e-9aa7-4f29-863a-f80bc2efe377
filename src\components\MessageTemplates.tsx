
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  MessageSquare,
  Plus,
  Edit,
  Copy,
  Eye,
  Phone,
  Mail,
  Smartphone,
  Lock,
  Edit3,
  Sparkles,
  Save
} from "lucide-react";
import { toast } from "sonner";

const MessageTemplates = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState(null);
  const [previewPlatform, setPreviewPlatform] = useState("whatsapp");

  const templates = [
    {
      id: 1,
      name: "Cabo Dreams Welcome",
      platform: "WhatsApp",
      resort: "Cabo Dreams Resort",
      staticPart: "🏖️ Hi! I just had an amazing stay at Cabo Dreams Resort and thought you'd love it too!",
      editablePart: "The beach was incredible and the service was top-notch. You should definitely check it out for your next vacation!",
      tags: ["{guest_name}", "{resort_name}", "{agent_name}"],
      lastUpdated: "2024-07-08",
      active: true
    },
    {
      id: 2,
      name: "Riviera Paradise SMS",
      platform: "SMS",
      resort: "Riviera Paradise",
      staticPart: "Hey! Just finished an incredible stay at Riviera Paradise Resort.",
      editablePart: "The pools were amazing and the food was fantastic. Thought you might be interested!",
      tags: ["{guest_name}", "{resort_name}", "{check_out_date}"],
      lastUpdated: "2024-07-07",
      active: true
    },
    {
      id: 3,
      name: "Cancun Luxury Email",
      platform: "Email",
      resort: "Cancun Luxury Suites",
      staticPart: "Subject: You'd Love This Resort!\n\nHi there!\n\nI just had the most wonderful experience at Cancun Luxury Suites.",
      editablePart: "The suites were spacious and elegant, and the location was perfect. I think you'd really enjoy a stay here!",
      tags: ["{guest_name}", "{resort_name}", "{special_offer}"],
      lastUpdated: "2024-07-06",
      active: false
    }
  ];

  const resorts = [
    "Cabo Dreams Resort",
    "Riviera Paradise",
    "Cancun Luxury Suites", 
    "Playa Bonita Resort"
  ];

  const availableTags = [
    "{guest_name}",
    "{resort_name}",
    "{agent_name}",
    "{check_in_date}",
    "{check_out_date}",
    "{special_offer}",
    "{booking_url}"
  ];

  const handleAddTemplate = () => {
    setEditingTemplate(null);
    setIsDialogOpen(true);
  };

  const handleEditTemplate = (template) => {
    setEditingTemplate(template);
    setIsDialogOpen(true);
  };

  const handleSaveTemplate = () => {
    toast.success(editingTemplate ? "Template updated successfully!" : "Template created successfully!");
    setIsDialogOpen(false);
    setEditingTemplate(null);
  };

  const copyTemplate = (template) => {
    const fullMessage = `${template.staticPart}\n\n${template.editablePart}`;
    navigator.clipboard.writeText(fullMessage);
    toast.success("Template copied to clipboard!");
  };

  const toggleTemplateStatus = (template) => {
    const newStatus = template.active ? "deactivated" : "activated";
    toast.success(`Template ${newStatus} successfully!`);
  };

  const getPlatformIcon = (platform) => {
    const icons = {
      "WhatsApp": MessageSquare,
      "SMS": Phone,
      "Email": Mail
    };
    return icons[platform] || MessageSquare;
  };

  const getPlatformColor = (platform) => {
    const colors = {
      "WhatsApp": "bg-green-100 text-green-700 border-green-200",
      "SMS": "bg-blue-100 text-blue-700 border-blue-200",
      "Email": "bg-purple-100 text-purple-700 border-purple-200"
    };
    return colors[platform] || "bg-gray-100 text-gray-700 border-gray-200";
  };

  const renderPreview = (template) => {
    const sampleData = {
      "{guest_name}": "John",
      "{resort_name}": template.resort,
      "{agent_name}": "Maria Rodriguez",
      "{check_out_date}": "July 15, 2024",
      "{special_offer}": "20% off your first booking"
    };

    let previewStatic = template.staticPart;
    let previewEditable = template.editablePart;

    Object.entries(sampleData).forEach(([tag, value]) => {
      previewStatic = previewStatic.replace(new RegExp(tag, 'g'), value);
      previewEditable = previewEditable.replace(new RegExp(tag, 'g'), value);
    });

    return { previewStatic, previewEditable };
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-slate-800">Message Templates</h2>
          <p className="text-slate-600 mt-1">Create and manage referral message templates</p>
        </div>
        <Button onClick={handleAddTemplate} className="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600">
          <Plus className="w-4 h-4 mr-2" />
          Add Template
        </Button>
      </div>

      {/* Template Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {templates.map((template) => {
          const PlatformIcon = getPlatformIcon(template.platform);
          const { previewStatic, previewEditable } = renderPreview(template);
          
          return (
            <Card key={template.id} className="bg-white/70 backdrop-blur-sm border-slate-200 hover:shadow-lg transition-all duration-200">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <PlatformIcon className="w-5 h-5 mr-2 text-slate-600" />
                    {template.name}
                  </CardTitle>
                  <Badge 
                    variant={template.active ? "default" : "secondary"}
                    className={template.active ? "bg-green-100 text-green-700 border-green-200" : ""}
                  >
                    {template.active ? "Active" : "Inactive"}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-600">{template.resort}</span>
                  <Badge className={getPlatformColor(template.platform)}>
                    {template.platform}
                  </Badge>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {/* Template Preview */}
                <div className="bg-slate-50 p-4 rounded-lg space-y-3">
                  <div className="flex items-center space-x-2 text-sm">
                    <Lock className="w-4 h-4 text-slate-500" />
                    <span className="text-slate-600 font-medium">Static Part (Locked)</span>
                  </div>
                  <div className="text-sm text-slate-700 bg-white p-3 rounded border-l-4 border-blue-500">
                    {previewStatic}
                  </div>
                  
                  <div className="flex items-center space-x-2 text-sm">
                    <Edit3 className="w-4 h-4 text-slate-500" />
                    <span className="text-slate-600 font-medium">Editable Part</span>
                  </div>
                  <div className="text-sm text-slate-700 bg-white p-3 rounded border-l-4 border-green-500">
                    {previewEditable}
                  </div>
                </div>

                {/* Tags */}
                <div>
                  <div className="flex items-center space-x-2 text-sm text-slate-600 mb-2">
                    <Sparkles className="w-4 h-4" />
                    <span>Available Tags</span>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {template.tags.map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Actions */}
                <div className="flex space-x-2 pt-3 border-t">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEditTemplate(template)}
                    className="flex-1"
                  >
                    <Edit className="w-3 h-3 mr-1" />
                    Edit
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyTemplate(template)}
                    className="flex-1"
                  >
                    <Copy className="w-3 h-3 mr-1" />
                    Copy
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => toggleTemplateStatus(template)}
                  >
                    {template.active ? "Deactivate" : "Activate"}
                  </Button>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Add/Edit Template Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingTemplate ? 'Edit Template' : 'Create New Template'}
            </DialogTitle>
            <DialogDescription>
              Design message templates with static and editable sections for different platforms
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-6 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="templateName">Template Name</Label>
                <Input 
                  id="templateName" 
                  placeholder="Template name"
                  defaultValue={editingTemplate?.name}
                />
              </div>
              <div>
                <Label htmlFor="templatePlatform">Platform</Label>
                <Select defaultValue={editingTemplate?.platform || "WhatsApp"}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select platform" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="WhatsApp">WhatsApp</SelectItem>
                    <SelectItem value="SMS">SMS</SelectItem>
                    <SelectItem value="Email">Email</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="templateResort">Assigned Resort</Label>
              <Select defaultValue={editingTemplate?.resort}>
                <SelectTrigger>
                  <SelectValue placeholder="Select resort" />
                </SelectTrigger>
                <SelectContent>
                  {resorts.map((resort) => (
                    <SelectItem key={resort} value={resort}>
                      {resort}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-4">
              <div>
                <div className="flex items-center space-x-2 mb-2">
                  <Lock className="w-4 h-4 text-slate-500" />
                  <Label htmlFor="staticPart">Static Part (Cannot be edited by guests)</Label>
                </div>
                <Textarea 
                  id="staticPart"
                  placeholder="Enter the locked portion of the message..."
                  rows={4}
                  defaultValue={editingTemplate?.staticPart}
                  className="bg-blue-50 border-blue-200"
                />
              </div>

              <div>
                <div className="flex items-center space-x-2 mb-2">
                  <Edit3 className="w-4 h-4 text-slate-500" />
                  <Label htmlFor="editablePart">Editable Part (Guests can customize)</Label>
                </div>
                <Textarea 
                  id="editablePart"
                  placeholder="Enter the customizable portion of the message..."
                  rows={4}
                  defaultValue={editingTemplate?.editablePart}
                  className="bg-green-50 border-green-200"
                />
              </div>
            </div>

            <div>
              <div className="flex items-center space-x-2 mb-3">
                <Sparkles className="w-4 h-4 text-slate-500" />
                <Label>Available Personalization Tags</Label>
              </div>
              <div className="grid grid-cols-4 gap-2">
                {availableTags.map((tag, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    className="text-xs justify-start"
                    onClick={() => {
                      navigator.clipboard.writeText(tag);
                      toast.success(`${tag} copied!`);
                    }}
                  >
                    {tag}
                  </Button>
                ))}
              </div>
            </div>

            {/* Preview */}
            <div className="border-t pt-6">
              <Label className="text-base font-semibold">Template Preview</Label>
              <div className="mt-3 bg-slate-50 p-4 rounded-lg">
                <div className="space-y-3">
                  <div className="bg-white p-3 rounded border-l-4 border-blue-500">
                    <div className="text-xs text-slate-500 mb-1">Static Part</div>
                    <div className="text-sm">🏖️ Hi John! I just had an amazing stay at Cabo Dreams Resort and thought you'd love it too!</div>
                  </div>
                  <div className="bg-white p-3 rounded border-l-4 border-green-500">
                    <div className="text-xs text-slate-500 mb-1">Editable Part</div>
                    <div className="text-sm">The beach was incredible and the service was top-notch. You should definitely check it out for your next vacation!</div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-6 border-t">
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveTemplate} className="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600">
                <Save className="w-4 h-4 mr-2" />
                {editingTemplate ? 'Update Template' : 'Create Template'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default MessageTemplates;
