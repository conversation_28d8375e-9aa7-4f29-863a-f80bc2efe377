import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import {
  BarChart3,
  TrendingUp,
  Users,
  Share2,
  Building2,
  MapPin,
  Calendar,
  Award,
  ArrowUp,
  ArrowDown,
  Download,
  Filter,
  Brain,
  MessageCircle,
  Phone,
  Mail
} from "lucide-react";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Bar<PERSON>hart, Bar, PieChart, Pie, Cell } from "recharts";
import { useState, useMemo, useEffect } from "react";
import DashboardFilters from "./ui/dashboard-filters";

// Redux imports
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { fetchDashboardData, updateFilters } from "@/redux/actions/dashboardActions";

const DashboardOverview = () => {
  // Redux state and dispatch
  const dispatch = useAppDispatch();
  const { stats, chartData, filters, loading, error } = useAppSelector((state) => state.dashboard);

  // Initialize dashboard data
  useEffect(() => {
    dispatch(fetchDashboardData() as any);
  }, [dispatch]);

  const handleFilterChange = (period: string, newDateRange?: { from: Date; to: Date }) => {
    const newFilters = {
      ...filters,
      period,
      ...(newDateRange && { dateRange: newDateRange })
    };
    dispatch(updateFilters(newFilters) as any);
  };

  // Generate dynamic data based on selected period
  const generateReferralData = useMemo(() => {
    const baseData = {
      daily: [
        { name: "12AM", referrals: 12, completed: 3 },
        { name: "6AM", referrals: 8, completed: 2 },
        { name: "12PM", referrals: 45, completed: 18 },
        { name: "6PM", referrals: 67, completed: 28 },
      ],
      weekly: [
        { name: "Mon", referrals: 45, completed: 12 },
        { name: "Tue", referrals: 52, completed: 18 },
        { name: "Wed", referrals: 38, completed: 15 },
        { name: "Thu", referrals: 64, completed: 22 },
        { name: "Fri", referrals: 78, completed: 28 },
        { name: "Sat", referrals: 89, completed: 35 },
        { name: "Sun", referrals: 67, completed: 24 }
      ],
      monthly: [
        { name: "Week 1", referrals: 245, completed: 89 },
        { name: "Week 2", referrals: 312, completed: 125 },
        { name: "Week 3", referrals: 278, completed: 98 },
        { name: "Week 4", referrals: 425, completed: 167 },
      ],
      custom: [
        { name: "Range Start", referrals: 156, completed: 62 },
        { name: "Mid Period", referrals: 289, completed: 115 },
        { name: "Range End", referrals: 234, completed: 89 },
      ]
    };
    return baseData[filters.period as keyof typeof baseData] || baseData.weekly;
  }, [filters.period]);

  const generatePlatformData = useMemo(() => {
    const baseData = {
      daily: [
        { name: "WhatsApp", value: 60, color: "#25D366" },
        { name: "SMS", value: 25, color: "#3B82F6" },
        { name: "Email", value: 15, color: "#8B5CF6" }
      ],
      weekly: [
        { name: "WhatsApp", value: 45, color: "#25D366" },
        { name: "SMS", value: 35, color: "#3B82F6" },
        { name: "Email", value: 20, color: "#8B5CF6" }
      ],
      monthly: [
        { name: "WhatsApp", value: 38, color: "#25D366" },
        { name: "SMS", value: 42, color: "#3B82F6" },
        { name: "Email", value: 20, color: "#8B5CF6" }
      ],
      custom: [
        { name: "WhatsApp", value: 52, color: "#25D366" },
        { name: "SMS", value: 28, color: "#3B82F6" },
        { name: "Email", value: 20, color: "#8B5CF6" }
      ]
    };
    return baseData[filters.period as keyof typeof baseData] || baseData.weekly;
  }, [filters.period]);

  const kpiData = [
    {
      title: "Total Guests",
      value: stats?.activeGuests?.toLocaleString() || "0",
      change: "+15.2%",
      trend: "up",
      icon: Users,
      color: "from-blue-500 to-cyan-500"
    },
    {
      title: "Total Referrals",
      value: stats?.totalReferrals?.toLocaleString() || "0",
      change: "+23.1%",
      trend: "up",
      icon: Share2,
      color: "from-green-500 to-emerald-500"
    },
    {
      title: "Completed Referrals",
      value: stats?.completedReferrals?.toLocaleString() || "0",
      change: "+18.5%",
      trend: "up",
      icon: Award,
      color: "from-purple-500 to-pink-500"
    },
    // {
    //   title: "Active Resorts",
    //   value: "24",
    //   change: "+2",
    //   trend: "up",
    //   icon: Building2,
    //   color: "from-orange-500 to-red-500"
    // }
  ];

  const topPerformers = [
    { 
      name: "Maria Rodriguez", 
      resort: "Cabo Dreams", 
      referrals: 156, 
      completion: 68,
      avatar: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=100&h=100&fit=crop&crop=face",
      whatsapp: 89,
      sms: 45,
      email: 22
    },
    { 
      name: "Carlos Santos", 
      resort: "Riviera Paradise", 
      referrals: 134, 
      completion: 61,
      avatar: "https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=100&h=100&fit=crop&crop=face",
      whatsapp: 78,
      sms: 38,
      email: 18
    },
    { 
      name: "Ana Martinez", 
      resort: "Cancun Luxury", 
      referrals: 128, 
      completion: 58,
      avatar: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=100&h=100&fit=crop&crop=face",
      whatsapp: 71,
      sms: 35,
      email: 22
    },
    { 
      name: "Roberto Kim", 
      resort: "Playa Bonita", 
      referrals: 112, 
      completion: 52,
      avatar: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=100&h=100&fit=crop&crop=face",
      whatsapp: 65,
      sms: 32,
      email: 15
    }
  ];

  const aiInsights = [
    "Maria Rodriguez has 40% higher conversion than average - consider promoting her strategies",
    "WhatsApp referrals show 25% better completion rates than SMS",
    "Weekend referrals have 18% higher conversion - optimize weekend campaigns",
    "Cabo Dreams resort shows declining trend - requires immediate attention"
  ];

  return (
    <div className="space-y-6">
      

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-slate-800">Dashboard Overview</h2>
          <p className="text-slate-600 mt-1">Monitor your resort referral performance</p>
        </div>
        <div className="flex justify-end">
        <DashboardFilters 
          onFilterChange={handleFilterChange}
        />
      </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {kpiData.map((kpi, index) => (
          <Card key={index} className="bg-white/70 backdrop-blur-sm border-slate-200 hover:shadow-lg transition-all duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 rounded-lg bg-gradient-to-r ${kpi.color}`}>
                  <kpi.icon className="w-6 h-6 text-white" />
                </div>
                <div className={`flex items-center text-sm font-medium ${
                  kpi.trend === 'up' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {kpi.trend === 'up' ? <ArrowUp className="w-4 h-4 mr-1" /> : <ArrowDown className="w-4 h-4 mr-1" />}
                  {kpi.change}
                </div>
              </div>
              <div>
                <div className="text-2xl font-bold text-slate-800">{kpi.value}</div>
                <div className="text-sm text-slate-600">{kpi.title}</div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="w-5 h-5 mr-2 text-blue-600" />
              Referral Trends
              <Badge variant="secondary" className="ml-2 text-xs">
                {filters.period.charAt(0).toUpperCase() + filters.period.slice(1)}
              </Badge>
            </CardTitle>
            <CardDescription>
              {filters.period === 'daily' ? 'Hourly' :
               filters.period === 'weekly' ? 'Daily' :
               filters.period === 'monthly' ? 'Weekly' : 'Custom'} referrals sent vs completed
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={chartData.length > 0 ? chartData : generateReferralData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                <XAxis dataKey="name" stroke="#64748b" />
                <YAxis stroke="#64748b" />
                <Tooltip />
                <Line type="monotone" dataKey="referrals" stroke="#3b82f6" strokeWidth={3} dot={{ fill: '#3b82f6' }} />
                <Line type="monotone" dataKey="completed" stroke="#10b981" strokeWidth={3} dot={{ fill: '#10b981' }} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Share2 className="w-5 h-5 mr-2 text-purple-600" />
              Platform Distribution
              <Badge variant="secondary" className="ml-2 text-xs">
                {filters.period.charAt(0).toUpperCase() + filters.period.slice(1)}
              </Badge>
            </CardTitle>
            <CardDescription>Referral sharing by platform for selected period</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={generatePlatformData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={120}
                  dataKey="value"
                  label={({ name, value }) => `${name} ${value}%`}
                >
                  {generatePlatformData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Top Performers */}
      <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Award className="w-5 h-5 mr-2 text-gold-600" />
            Top Performing Sales Representative
          </CardTitle>
          <CardDescription>Highest referral conversion rates this month</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {topPerformers.map((performer, index) => (
              <div key={index} className="flex items-center justify-between p-6 bg-slate-50 rounded-xl hover:shadow-md transition-all duration-200">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-full font-bold text-sm">
                    #{index + 1}
                  </div>
                  <Avatar className="w-12 h-12">
                    <AvatarImage src={performer.avatar} alt={performer.name} />
                    <AvatarFallback className="bg-gradient-to-r from-blue-500 to-cyan-500 text-white font-semibold">
                      {performer.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="font-semibold text-slate-800 text-lg">{performer.name}</div>
                    <div className="text-sm text-slate-600 flex items-center">
                      <MapPin className="w-3 h-3 mr-1" />
                      {performer.resort}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-6">
                  {/* Platform Counts */}
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-1 bg-green-100 px-2 py-1 rounded-lg">
                      <MessageCircle className="w-4 h-4 text-green-600" />
                      <span className="text-sm font-medium text-green-700">{performer.whatsapp}</span>
                    </div>
                    <div className="flex items-center space-x-1 bg-blue-100 px-2 py-1 rounded-lg">
                      <Phone className="w-4 h-4 text-blue-600" />
                      <span className="text-sm font-medium text-blue-700">{performer.sms}</span>
                    </div>
                    <div className="flex items-center space-x-1 bg-purple-100 px-2 py-1 rounded-lg">
                      <Mail className="w-4 h-4 text-purple-600" />
                      <span className="text-sm font-medium text-purple-700">{performer.email}</span>
                    </div>
                  </div>
                  {/* Performance Stats */}
                  <div className="text-right">
                    <div className="font-semibold text-slate-800 text-lg">{performer.referrals} referrals</div>
                    <div className="text-sm text-green-600 font-medium">{performer.completion}% completion</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DashboardOverview;