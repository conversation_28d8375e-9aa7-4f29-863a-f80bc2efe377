import { NotificationsState, Action } from '../types';
import * as actionTypes from '../constants/actionTypes';

const initialState: NotificationsState = {
  notifications: [],
  unreadCount: 0,
  loading: false,
  error: null
};

const notificationsReducer = (state = initialState, action: Action): NotificationsState => {
  switch (action.type) {
    case actionTypes.FETCH_NOTIFICATIONS_REQUEST:
    case actionTypes.ADD_NOTIFICATION_REQUEST:
    case actionTypes.DELETE_NOTIFICATION_REQUEST:
      return {
        ...state,
        loading: true,
        error: null
      };

    case actionTypes.FETCH_NOTIFICATIONS_SUCCESS:
      return {
        ...state,
        loading: false,
        notifications: action.payload,
        unreadCount: action.payload.filter((notification: any) => !notification.isRead).length,
        error: null
      };

    case actionTypes.ADD_NOTIFICATION_SUCCESS:
      const newNotifications = [...state.notifications, action.payload];
      return {
        ...state,
        loading: false,
        notifications: newNotifications,
        unreadCount: newNotifications.filter(notification => !notification.isRead).length,
        error: null
      };

    case actionTypes.MARK_NOTIFICATION_READ:
      const updatedNotifications = state.notifications.map(notification =>
        notification.id === action.payload
          ? { ...notification, isRead: true }
          : notification
      );
      return {
        ...state,
        notifications: updatedNotifications,
        unreadCount: updatedNotifications.filter(notification => !notification.isRead).length
      };

    case actionTypes.DELETE_NOTIFICATION_SUCCESS:
      const filteredNotifications = state.notifications.filter(
        notification => notification.id !== action.payload
      );
      return {
        ...state,
        loading: false,
        notifications: filteredNotifications,
        unreadCount: filteredNotifications.filter(notification => !notification.isRead).length,
        error: null
      };

    case actionTypes.FETCH_NOTIFICATIONS_FAILURE:
    case actionTypes.ADD_NOTIFICATION_FAILURE:
    case actionTypes.DELETE_NOTIFICATION_FAILURE:
      return {
        ...state,
        loading: false,
        error: action.payload
      };

    default:
      return state;
  }
};

export default notificationsReducer;
