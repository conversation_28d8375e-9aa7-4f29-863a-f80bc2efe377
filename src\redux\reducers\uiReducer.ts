import { UIState, Action } from '../types';
import * as actionTypes from '../constants/actionTypes';

const initialState: UIState = {
  activeTab: 'dashboard',
  isSidebarOpen: false,
  loading: false,
  error: null
};

const uiReducer = (state = initialState, action: Action): UIState => {
  switch (action.type) {
    case actionTypes.SET_ACTIVE_TAB:
      return {
        ...state,
        activeTab: action.payload
      };

    case actionTypes.TOGGLE_SIDEBAR:
      return {
        ...state,
        isSidebarOpen: !state.isSidebarOpen
      };

    case actionTypes.SET_LOADING:
      return {
        ...state,
        loading: action.payload
      };

    case actionTypes.SET_ERROR:
      return {
        ...state,
        error: action.payload
      };

    case actionTypes.CLEAR_ERROR:
      return {
        ...state,
        error: null
      };

    default:
      return state;
  }
};

export default uiReducer;
