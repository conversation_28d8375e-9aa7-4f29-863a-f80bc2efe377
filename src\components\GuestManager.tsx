import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Users,
  Search,
  Filter,
  Download,
  Eye,
  Trash2,
  UserCheck,
  UserX,
  Calendar,
  MapPin,
  Phone,
  Mail,
  Share2,
  AlertTriangle
} from "lucide-react";
import { toast } from "sonner";

const GuestManager = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [resortFilter, setResortFilter] = useState("all");
  const [selectedGuest, setSelectedGuest] = useState(null);
  const [isDetailOpen, setIsDetailOpen] = useState(false);

  const guests = [
    {
      id: "guest001",
      name: "John Smith",
      email: "<EMAIL>",
      phone: "******-0234",
      loginMethod: "Google",
      resort: "Cabo Dreams Resort",
      referringAgent: "Vikas Patel",
      joinDate: "2024-07-05",
      lastActivity: "2024-07-08",
      status: "Active",
      totalReferrals: 3,
      completedReferrals: 1,
      referrals: [
        { name: "Sarah Johnson", status: "Completed", date: "2024-07-07" },
        { name: "Mike Wilson", status: "Clicked", date: "2024-07-08" },
        { name: "Lisa Chen", status: "Sent", date: "2024-07-08" }
      ]
    },
    {
      id: "guest002",
      name: "Maria Garcia",
      email: "<EMAIL>",
      phone: "******-0567",
      loginMethod: "Email",
      resort: "Riviera Paradise",
      referringAgent: "Maria Rodriguez",
      joinDate: "2024-07-03",
      lastActivity: "2024-07-07",
      status: "Active",
      totalReferrals: 5,
      completedReferrals: 2,
      referrals: [
        { name: "Carlos Rivera", status: "Completed", date: "2024-07-06" },
        { name: "Emma Thompson", status: "Completed", date: "2024-07-07" },
        { name: "David Brown", status: "Clicked", date: "2024-07-07" },
        { name: "Anna Wilson", status: "Failed", date: "2024-07-05" },
        { name: "Tom Davis", status: "Sent", date: "2024-07-08" }
      ]
    },
    {
      id: "guest003",
      name: "David Wilson",
      email: "<EMAIL>",
      phone: "******-0890",
      loginMethod: "Anonymous",
      resort: "Cancun Luxury Suites",
      referringAgent: "Carlos Santos",
      joinDate: "2024-07-04",
      lastActivity: "2024-07-04",
      status: "Inactive",
      totalReferrals: 1,
      completedReferrals: 1,
      referrals: [
        { name: "Jennifer Lee", status: "Completed", date: "2024-07-06" }
      ]
    }
  ];

  const filteredGuests = guests.filter(guest => {
    const matchesSearch = 
      guest.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      guest.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      guest.referringAgent.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === "all" || guest.status.toLowerCase() === statusFilter;
    const matchesResort = resortFilter === "all" || guest.resort === resortFilter;
    
    return matchesSearch && matchesStatus && matchesResort;
  });

  const getStatusBadge = (status) => {
    const statusConfig = {
      "Active": { color: "bg-green-100 text-green-700 border-green-200", icon: UserCheck },
      "Inactive": { color: "bg-gray-100 text-gray-700 border-gray-200", icon: UserX }
    };
    return statusConfig[status] || statusConfig["Active"];
  };

  const handleGuestClick = (guest) => {
    setSelectedGuest(guest);
    setIsDetailOpen(true);
  };

  const handleDelete = (guestId) => {
    toast.success(`Guest ${guestId} deleted successfully (GDPR compliance)`);
  };

  const exportData = () => {
    toast.success("Guest data exported successfully!");
  };

  const resorts = [...new Set(guests.map(g => g.resort))];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-slate-800">Guest Management</h2>
          <p className="text-slate-600 mt-1">Manage guest accounts and referral activity</p>
        </div>
        <Button onClick={exportData} className="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600">
          <Download className="w-4 h-4 mr-2" />
          Export Data
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Total Guests</p>
                <p className="text-2xl font-bold text-slate-800">{guests.length}</p>
              </div>
              <Users className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Active Guests</p>
                <p className="text-2xl font-bold text-slate-800">
                  {guests.filter(g => g.status === 'Active').length}
                </p>
              </div>
              <UserCheck className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Total Referrals</p>
                <p className="text-2xl font-bold text-slate-800">
                  {guests.reduce((sum, g) => sum + g.totalReferrals, 0)}
                </p>
              </div>
              <Share2 className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Avg. Referrals/Guest</p>
                <p className="text-2xl font-bold text-slate-800">
                  {Math.round((guests.reduce((sum, g) => sum + g.totalReferrals, 0) / guests.length) * 10) / 10}
                </p>
              </div>
              <Share2 className="w-8 h-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
        <CardContent className="p-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[300px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                <Input
                  placeholder="Search by name, email, or sales reps..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
            <Select value={resortFilter} onValueChange={setResortFilter}>
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Resort" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Resorts</SelectItem>
                {resorts.map((resort) => (
                  <SelectItem key={resort} value={resort}>
                    {resort}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Guests Table */}
      <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
        <CardHeader>
          <CardTitle>Guest Directory</CardTitle>
          <CardDescription>Manage guest accounts and track referral activity</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Guest Info</TableHead>
                <TableHead>Contact</TableHead>
                <TableHead>Referral Sales Reps</TableHead>
                <TableHead>Activity</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredGuests.map((guest) => {
                const statusConfig = getStatusBadge(guest.status);
                const StatusIcon = statusConfig.icon;
                
                return (
                  <TableRow key={guest.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium text-slate-800">{guest.name}</div>
                        <div className="text-sm text-slate-600">ID: {guest.id}</div>
                        <div className="text-xs text-slate-500 flex items-center mt-1">
                          <Calendar className="w-3 h-3 mr-1" />
                          Joined: {guest.joinDate}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="text-sm text-slate-600 flex items-center">
                          <Mail className="w-3 h-3 mr-1" />
                          {guest.email}
                        </div>
                        <div className="text-sm text-slate-600 flex items-center">
                          <Phone className="w-3 h-3 mr-1" />
                          {guest.phone}
                        </div>
                        <div className="text-xs text-slate-500 mt-1">
                          Login: {guest.loginMethod}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium text-slate-800">{guest.referringAgent}</div>
                        <div className="text-sm text-slate-600 flex items-center">
                          <MapPin className="w-3 h-3 mr-1" />
                          {guest.resort}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="text-sm font-medium text-slate-800">
                          {guest.totalReferrals} referrals sent
                        </div>
                        <div className="text-sm text-green-600">
                          {guest.completedReferrals} completed
                        </div>
                        <div className="text-xs text-slate-500">
                          Last: {guest.lastActivity}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <StatusIcon className="w-4 h-4" />
                        <Badge className={statusConfig.color}>
                          {guest.status}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleGuestClick(guest)}
                        >
                          <Eye className="w-3 h-3" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(guest.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Guest Detail Dialog */}
      <Dialog open={isDetailOpen} onOpenChange={setIsDetailOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Guest Details</DialogTitle>
            <DialogDescription>
              {selectedGuest?.name ? `Complete profile for ${selectedGuest.name}` : 'Guest information'}
            </DialogDescription>
          </DialogHeader>
          
          {selectedGuest && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-slate-600">Name</Label>
                  <p className="text-slate-800">{selectedGuest.name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-slate-600">Email</Label>
                  <p className="text-slate-800">{selectedGuest.email}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-slate-600">Phone</Label>
                  <p className="text-slate-800">{selectedGuest.phone}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-slate-600">Status</Label>
                  <p className="text-slate-800">{selectedGuest.status}</p>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium text-slate-600 mb-3 block">Referral Activity</Label>
                <div className="space-y-2">
                  {selectedGuest.referrals.map((referral, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                      <div>
                        <div className="font-medium text-slate-800">{referral.name}</div>
                        <div className="text-sm text-slate-600">{referral.date}</div>
                      </div>
                      <Badge className={
                        referral.status === 'Completed' ? 'bg-green-100 text-green-700' :
                        referral.status === 'Clicked' ? 'bg-blue-100 text-blue-700' :
                        referral.status === 'Failed' ? 'bg-red-100 text-red-700' :
                        'bg-yellow-100 text-yellow-700'
                      }>
                        {referral.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex justify-end space-x-2 pt-4 border-t">
                <Button variant="outline" onClick={() => setIsDetailOpen(false)}>
                  Close
                </Button>
                <Button 
                  variant="destructive"
                  onClick={() => {
                    handleDelete(selectedGuest.id);
                    setIsDetailOpen(false);
                  }}
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete (GDPR)
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default GuestManager;
