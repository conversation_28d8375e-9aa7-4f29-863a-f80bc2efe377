import { Dispatch } from 'redux';
import { Agent, Action } from '../types';
import * as actionTypes from '../constants/actionTypes';

// Dummy data
const dummyAgents: Agent[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '******-0123',
    role: 'Senior Sales Agent',
    status: 'active',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
    resort: 'Vida Resort Miami',
    totalReferrals: 45,
    completedReferrals: 38,
    revenue: 125000,
    commission: 12500,
    joinDate: '2023-01-15',
    lastActive: '2024-01-20T10:30:00Z',
    referralCode: 'JS2024',
    createdAt: '2023-01-15T09:00:00Z',
    updatedAt: '2024-01-20T10:30:00Z'
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '******-0124',
    role: 'Sales Agent',
    status: 'active',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
    resort: 'Vida Resort Orlando',
    totalReferrals: 32,
    completedReferrals: 28,
    revenue: 89000,
    commission: 8900,
    joinDate: '2023-03-20',
    lastActive: '2024-01-20T14:15:00Z',
    referralCode: 'SJ2024',
    createdAt: '2023-03-20T09:00:00Z',
    updatedAt: '2024-01-20T14:15:00Z'
  },
  {
    id: '3',
    name: 'Mike Davis',
    email: '<EMAIL>',
    phone: '******-0125',
    role: 'Junior Sales Agent',
    status: 'pending',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
    resort: 'Vida Resort Miami',
    totalReferrals: 12,
    completedReferrals: 8,
    revenue: 25000,
    commission: 2500,
    joinDate: '2023-11-10',
    lastActive: '2024-01-19T16:45:00Z',
    referralCode: 'MD2024',
    createdAt: '2023-11-10T09:00:00Z',
    updatedAt: '2024-01-19T16:45:00Z'
  },
  {
    id: '4',
    name: 'Emily Chen',
    email: '<EMAIL>',
    phone: '******-0126',
    role: 'Senior Sales Agent',
    status: 'active',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
    resort: 'Vida Resort Las Vegas',
    totalReferrals: 67,
    completedReferrals: 59,
    revenue: 198000,
    commission: 19800,
    joinDate: '2022-08-05',
    lastActive: '2024-01-20T11:20:00Z',
    referralCode: 'EC2024',
    createdAt: '2022-08-05T09:00:00Z',
    updatedAt: '2024-01-20T11:20:00Z'
  }
];

// Action Creators
export const fetchAgentsRequest = (): Action => ({
  type: actionTypes.FETCH_AGENTS_REQUEST
});

export const fetchAgentsSuccess = (agents: Agent[]): Action => ({
  type: actionTypes.FETCH_AGENTS_SUCCESS,
  payload: agents
});

export const fetchAgentsFailure = (error: string): Action => ({
  type: actionTypes.FETCH_AGENTS_FAILURE,
  payload: error
});

export const addAgentRequest = (): Action => ({
  type: actionTypes.ADD_AGENT_REQUEST
});

export const addAgentSuccess = (agent: Agent): Action => ({
  type: actionTypes.ADD_AGENT_SUCCESS,
  payload: agent
});

export const addAgentFailure = (error: string): Action => ({
  type: actionTypes.ADD_AGENT_FAILURE,
  payload: error
});

export const updateAgentRequest = (): Action => ({
  type: actionTypes.UPDATE_AGENT_REQUEST
});

export const updateAgentSuccess = (agent: Agent): Action => ({
  type: actionTypes.UPDATE_AGENT_SUCCESS,
  payload: agent
});

export const updateAgentFailure = (error: string): Action => ({
  type: actionTypes.UPDATE_AGENT_FAILURE,
  payload: error
});

export const deleteAgentRequest = (): Action => ({
  type: actionTypes.DELETE_AGENT_REQUEST
});

export const deleteAgentSuccess = (agentId: string): Action => ({
  type: actionTypes.DELETE_AGENT_SUCCESS,
  payload: agentId
});

export const deleteAgentFailure = (error: string): Action => ({
  type: actionTypes.DELETE_AGENT_FAILURE,
  payload: error
});

export const setSelectedAgent = (agent: Agent | null): Action => ({
  type: actionTypes.SET_SELECTED_AGENT,
  payload: agent
});

// Thunk Actions
export const fetchAgents = () => {
  return (dispatch: Dispatch) => {
    dispatch(fetchAgentsRequest());
    
    // Simulate API call
    setTimeout(() => {
      try {
        dispatch(fetchAgentsSuccess(dummyAgents));
      } catch (error) {
        dispatch(fetchAgentsFailure('Failed to fetch agents'));
      }
    }, 1000);
  };
};

export const addAgent = (agentData: Partial<Agent>) => {
  return (dispatch: Dispatch) => {
    dispatch(addAgentRequest());
    
    // Simulate API call
    setTimeout(() => {
      try {
        const newAgent: Agent = {
          id: Date.now().toString(),
          name: agentData.name || '',
          email: agentData.email || '',
          phone: agentData.phone || '',
          role: agentData.role || 'Sales Agent',
          status: 'pending',
          resort: agentData.resort || '',
          totalReferrals: 0,
          completedReferrals: 0,
          revenue: 0,
          commission: 0,
          joinDate: new Date().toISOString().split('T')[0],
          lastActive: new Date().toISOString(),
          referralCode: `${agentData.name?.substring(0, 2).toUpperCase()}${Date.now().toString().slice(-4)}`,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        dispatch(addAgentSuccess(newAgent));
      } catch (error) {
        dispatch(addAgentFailure('Failed to add agent'));
      }
    }, 1000);
  };
};

export const updateAgent = (agentId: string, agentData: Partial<Agent>) => {
  return (dispatch: Dispatch) => {
    dispatch(updateAgentRequest());
    
    // Simulate API call
    setTimeout(() => {
      try {
        const updatedAgent: Agent = {
          ...dummyAgents.find(agent => agent.id === agentId)!,
          ...agentData,
          updatedAt: new Date().toISOString()
        };
        
        dispatch(updateAgentSuccess(updatedAgent));
      } catch (error) {
        dispatch(updateAgentFailure('Failed to update agent'));
      }
    }, 1000);
  };
};

export const deleteAgent = (agentId: string) => {
  return (dispatch: Dispatch) => {
    dispatch(deleteAgentRequest());

    // Simulate API call
    setTimeout(() => {
      try {
        dispatch(deleteAgentSuccess(agentId));
      } catch (error) {
        dispatch(deleteAgentFailure('Failed to delete agent'));
      }
    }, 1000);
  };
};
