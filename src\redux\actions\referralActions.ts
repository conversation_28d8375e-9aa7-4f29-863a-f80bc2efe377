import { Dispatch } from 'redux';
import { Referral, Action } from '../types';
import * as actionTypes from '../constants/actionTypes';

// Dummy data
const dummyReferrals: Referral[] = [
  {
    id: '1',
    referrerName: '<PERSON>',
    referrerEmail: '<EMAIL>',
    refereeName: '<PERSON>',
    refereeEmail: '<EMAIL>',
    refereePhone: '******-0201',
    status: 'completed',
    reward: 500,
    commission: 50,
    completedDate: '2024-01-15',
    resort: 'Vida Resort Miami',
    notes: 'Excellent referral, very satisfied customer',
    createdAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-01-15T14:30:00Z'
  },
  {
    id: '2',
    referrerName: '<PERSON>',
    referrerEmail: '<EMAIL>',
    refereeName: '<PERSON>',
    refereeEmail: '<EMAIL>',
    refereePhone: '******-0202',
    status: 'pending',
    reward: 750,
    commission: 75,
    resort: 'Vida Resort Orlando',
    notes: 'Follow up scheduled for next week',
    createdAt: '2024-01-18T10:15:00Z',
    updatedAt: '2024-01-18T10:15:00Z'
  },
  {
    id: '3',
    referrerName: '<PERSON> Chen',
    referrerEmail: '<EMAIL>',
    refereeName: 'Carol Davis',
    refereeEmail: '<EMAIL>',
    refereePhone: '******-0203',
    status: 'completed',
    reward: 1000,
    commission: 100,
    completedDate: '2024-01-12',
    resort: 'Vida Resort Las Vegas',
    notes: 'Premium package referral',
    createdAt: '2024-01-08T11:20:00Z',
    updatedAt: '2024-01-12T16:45:00Z'
  },
  {
    id: '4',
    referrerName: 'Mike Davis',
    referrerEmail: '<EMAIL>',
    refereeName: 'David Brown',
    refereeEmail: '<EMAIL>',
    refereePhone: '******-0204',
    status: 'cancelled',
    reward: 500,
    commission: 50,
    resort: 'Vida Resort Miami',
    notes: 'Customer changed mind about vacation plans',
    createdAt: '2024-01-05T13:30:00Z',
    updatedAt: '2024-01-14T09:15:00Z'
  }
];

// Action Creators
export const fetchReferralsRequest = (): Action => ({
  type: actionTypes.FETCH_REFERRALS_REQUEST
});

export const fetchReferralsSuccess = (referrals: Referral[]): Action => ({
  type: actionTypes.FETCH_REFERRALS_SUCCESS,
  payload: referrals
});

export const fetchReferralsFailure = (error: string): Action => ({
  type: actionTypes.FETCH_REFERRALS_FAILURE,
  payload: error
});

export const addReferralRequest = (): Action => ({
  type: actionTypes.ADD_REFERRAL_REQUEST
});

export const addReferralSuccess = (referral: Referral): Action => ({
  type: actionTypes.ADD_REFERRAL_SUCCESS,
  payload: referral
});

export const addReferralFailure = (error: string): Action => ({
  type: actionTypes.ADD_REFERRAL_FAILURE,
  payload: error
});

export const updateReferralRequest = (): Action => ({
  type: actionTypes.UPDATE_REFERRAL_REQUEST
});

export const updateReferralSuccess = (referral: Referral): Action => ({
  type: actionTypes.UPDATE_REFERRAL_SUCCESS,
  payload: referral
});

export const updateReferralFailure = (error: string): Action => ({
  type: actionTypes.UPDATE_REFERRAL_FAILURE,
  payload: error
});

export const deleteReferralRequest = (): Action => ({
  type: actionTypes.DELETE_REFERRAL_REQUEST
});

export const deleteReferralSuccess = (referralId: string): Action => ({
  type: actionTypes.DELETE_REFERRAL_SUCCESS,
  payload: referralId
});

export const deleteReferralFailure = (error: string): Action => ({
  type: actionTypes.DELETE_REFERRAL_FAILURE,
  payload: error
});

// Thunk Actions
export const fetchReferrals = () => {
  return (dispatch: Dispatch) => {
    dispatch(fetchReferralsRequest());
    
    // Simulate API call
    setTimeout(() => {
      try {
        dispatch(fetchReferralsSuccess(dummyReferrals));
      } catch (error) {
        dispatch(fetchReferralsFailure('Failed to fetch referrals'));
      }
    }, 1000);
  };
};

export const addReferral = (referralData: Partial<Referral>) => {
  return (dispatch: Dispatch) => {
    dispatch(addReferralRequest());
    
    // Simulate API call
    setTimeout(() => {
      try {
        const newReferral: Referral = {
          id: Date.now().toString(),
          referrerName: referralData.referrerName || '',
          referrerEmail: referralData.referrerEmail || '',
          refereeName: referralData.refereeName || '',
          refereeEmail: referralData.refereeEmail || '',
          refereePhone: referralData.refereePhone || '',
          status: 'pending',
          reward: referralData.reward || 500,
          commission: referralData.commission || 50,
          resort: referralData.resort || '',
          notes: referralData.notes || '',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        dispatch(addReferralSuccess(newReferral));
      } catch (error) {
        dispatch(addReferralFailure('Failed to add referral'));
      }
    }, 1000);
  };
};

export const updateReferral = (referralId: string, referralData: Partial<Referral>) => {
  return (dispatch: Dispatch) => {
    dispatch(updateReferralRequest());
    
    // Simulate API call
    setTimeout(() => {
      try {
        const updatedReferral: Referral = {
          ...dummyReferrals.find(referral => referral.id === referralId)!,
          ...referralData,
          updatedAt: new Date().toISOString()
        };
        
        dispatch(updateReferralSuccess(updatedReferral));
      } catch (error) {
        dispatch(updateReferralFailure('Failed to update referral'));
      }
    }, 1000);
  };
};

export const deleteReferral = (referralId: string) => {
  return (dispatch: Dispatch) => {
    dispatch(deleteReferralRequest());
    
    // Simulate API call
    setTimeout(() => {
      try {
        dispatch(deleteReferralSuccess(referralId));
      } catch (error) {
        dispatch(deleteReferralFailure('Failed to delete referral'));
      }
    }, 1000);
  };
};
