import { Action } from '../types';
import * as actionTypes from '../constants/actionTypes';

// Action Creators
export const setActiveTab = (tab: string): Action => ({
  type: actionTypes.SET_ACTIVE_TAB,
  payload: tab
});

export const toggleSidebar = (): Action => ({
  type: actionTypes.TOGGLE_SIDEBAR
});

export const setLoading = (loading: boolean): Action => ({
  type: actionTypes.SET_LOADING,
  payload: loading
});

export const setError = (error: string): Action => ({
  type: actionTypes.SET_ERROR,
  payload: error
});

export const clearError = (): Action => ({
  type: actionTypes.CLEAR_ERROR
});
