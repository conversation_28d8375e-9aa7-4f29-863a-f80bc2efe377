
import React from "react";
import { cn } from "@/lib/utils";
import { ImageIcon } from "lucide-react";

interface ImagePlaceholderProps {
  src?: string;
  alt?: string;
  className?: string;
  fallbackIcon?: React.ReactNode;
  aspectRatio?: "square" | "video" | "portrait" | "landscape";
}

export const ImagePlaceholder: React.FC<ImagePlaceholderProps> = ({
  src,
  alt = "Image",
  className,
  fallbackIcon,
  aspectRatio = "landscape",
}) => {
  const aspectRatioClasses = {
    square: "aspect-square",
    video: "aspect-video",
    portrait: "aspect-[3/4]",
    landscape: "aspect-[4/3]",
  };

  if (src) {
    return (
      <img
        src={src}
        alt={alt}
        className={cn(
          "w-full h-full object-cover rounded-lg",
          aspectRatioClasses[aspectRatio],
          className
        )}
        loading="lazy"
      />
    );
  }

  return (
    <div
      className={cn(
        "w-full bg-muted rounded-lg flex items-center justify-center",
        aspectRatioClasses[aspectRatio],
        className
      )}
    >
      {fallbackIcon || <ImageIcon className="w-12 h-12 text-muted-foreground" />}
    </div>
  );
};
