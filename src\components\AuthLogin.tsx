
import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { FormField } from "@/components/ui/form-field";
import { Badge } from "@/components/ui/badge";
import { Building2, Shield, Eye, Lock, AlertCircle } from "lucide-react";
import { NotificationToast } from "@/components/ui/notification-toast";
import { ImagePlaceholder } from "@/components/ui/image-placeholder";

interface AuthLoginProps {
  onLogin: (user: { email: string; role: string; name: string }) => void;
}

const AuthLogin = ({ onLogin }: AuthLoginProps) => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{ email?: string; password?: string }>({});

  const validateForm = () => {
    const newErrors: { email?: string; password?: string } = {};
    
    if (!email) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = "Please enter a valid email address";
    }
    
    if (!password) {
      newErrors.password = "Password is required";
    } else if (password.length < 6) {
      newErrors.password = "Password must be at least 6 characters";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      NotificationToast.error("Validation Error", "Please fix the errors below");
      return;
    }
    
    setIsLoading(true);

    try {
      // Simulate authentication
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Mock role assignment based on email
      let role = "viewer";
      let name = "User";
      
      if (email.includes("admin@")) {
        role = "super_admin";
        name = "Super Admin";
      } else if (email.includes("resort@")) {
        role = "resort_admin";
        name = "Resort Admin";
      } else {
        role = "viewer";
        name = "Agent";
      }

      onLogin({ email, role, name });
      NotificationToast.success("Welcome back!", `Successfully logged in as ${name}`);
    } catch (error) {
      NotificationToast.error("Login Failed", "Please check your credentials and try again");
    } finally {
      setIsLoading(false);
    }
  };

  const getRoleBadge = (role: string) => {
    const roleConfig = {
      super_admin: { label: "Super Admin", color: "bg-red-100 text-red-700 border-red-200", icon: Shield },
      resort_admin: { label: "Resort Admin", color: "bg-blue-100 text-blue-700 border-blue-200", icon: Building2 },
      viewer: { label: "Sales Representative", color: "bg-gray-100 text-gray-700 border-gray-200", icon: Eye }

    };
    return roleConfig[role as keyof typeof roleConfig] || roleConfig.viewer;
  };

  return (
    <div className="relative">
      <div className="absolute w-screen h-screen top-0 left-0">
  <video
    src="videos/HOME.mp4"
    autoPlay
    muted
    playsInline
    loop
    className="w-full h-full object-cover"
  />
</div>

    <div className="min-h-screen bg-gradient-to-br z-50 from-slate-50 via-blue-50 to-cyan-50 flex items-center justify-center p-4">
      <div className="w-full max-w-6xl grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left side - Branding */}
        <div className="hidden z-50 lg:flex flex-col justify-center items-center space-y-8">
          <div className="text-center space-y-4">
            <div className="flex flex-col gap-4 items-center justify-center space-x-3 mb-6">
              <div className="w-40 h-36  p-4  rounded-2xl flex items-center justify-center">
                <img src="images/vv-logo.png" className=""/>
              </div>
              <h1 className="text-4xl font-bold text-white">
                VIDA Resort CMS
              </h1>
            </div>
            <p className="text-xl text-white max-w-md">
              Streamline your resort operations with our comprehensive content management system
            </p>
          </div>
          
          {/* <div className="w-full max-w-md">
            <ImagePlaceholder
              src="https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
              alt="Luxury resort view"
              aspectRatio="landscape"
              className="rounded-2xl shadow-2xl"
            />
          </div> */}
        </div>

        {/* Right side - Login Form */}
        <div className="flex items-center justify-center z-50">
          <Card className="w-full max-w-md bg-white/90 backdrop-blur-md border-slate-200 shadow-xl">
            <CardHeader className="text-center pb-6">
              <div className="flex items-center justify-center space-x-2 mb-4 lg:hidden">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-lg flex items-center justify-center">
                  <Building2 className="w-6 h-6 text-white" />
                </div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent">
                  VIDA Resort CMS
                </h1>
              </div>
              <CardTitle className="text-2xl font-bold">Welcome Back</CardTitle>
              <CardDescription className="text-base">
                Sign in to access your dashboard
              </CardDescription>
            </CardHeader>
            
            <CardContent className="space-y-6">
              <form onSubmit={handleLogin} className="space-y-6">
                <FormField
                  label="Email Address"
                  type="email"
                  value={email}
                  onChange={setEmail}
                  placeholder="<EMAIL>"
                  error={errors.email}
                  required
                />
                
                <FormField
                  label="Password"
                  type="password"
                  value={password}
                  onChange={setPassword}
                  placeholder="Enter your password"
                  error={errors.password}
                  required
                />

                <Button 
                  type="submit" 
                  className="w-full h-12 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white font-semibold text-base"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span>Signing In...</span>
                    </div>
                  ) : (
                    "Sign In"
                  )}
                </Button>
              </form>

              <div className="pt-6 border-t border-slate-200">
                <p className="text-sm text-slate-600 mb-4 font-medium">Demo Accounts:</p>
                <div className="space-y-3">
                  {[
                    { email: "<EMAIL>", role: "super_admin" },
                    { email: "<EMAIL>", role: "resort_admin" },
                    { email: "<EMAIL>", role: "viewer" }
                  ].map((demo) => {
                    const config = getRoleBadge(demo.role);
                    const Icon = config.icon;
                    return (
                      <div 
                        key={demo.email} 
                        className="flex items-center justify-between p-3 bg-slate-50 rounded-lg hover:bg-slate-100 transition-colors cursor-pointer"
                        onClick={() => {
                          setEmail(demo.email);
                          setPassword("demo123");
                          setErrors({});
                        }}
                      >
                        <span className="text-sm font-medium text-slate-700">{demo.email}</span>
                        <Badge className={`${config.color} text-xs font-medium`}>
                          <Icon className="w-3 h-3 mr-1" />
                          {config.label}
                        </Badge>
                      </div>
                    );
                  })}
                </div>
                
                <div className="mt-4 flex items-start space-x-2 p-3 bg-blue-50 rounded-lg">
                  <AlertCircle className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <p className="text-xs text-blue-700">
                    Click on any demo account above to auto-fill credentials, then click "Sign In"
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
    </div>
  );
};

export default AuthLogin;
