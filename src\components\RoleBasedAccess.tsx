import { ReactNode } from 'react';
import { useAuth0Extended } from '@/hooks/useAuth0Extended';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertCircle } from 'lucide-react';

interface RoleBasedAccessProps {
  children: ReactNode;
  allowedRoles?: string[];
  requiredPermissions?: string[];
  fallback?: ReactNode;
  showFallback?: boolean;
}

const RoleBasedAccess = ({ 
  children, 
  allowedRoles = [], 
  requiredPermissions = [],
  fallback,
  showFallback = true
}: RoleBasedAccessProps) => {
  const { user, hasRole, hasPermission } = useAuth0Extended();

  // Check if user has any of the allowed roles
  const hasAllowedRole = allowedRoles.length === 0 || allowedRoles.some(role => hasRole(role));

  // Check if user has all required permissions
  const hasRequiredPermissions = requiredPermissions.length === 0 || 
    requiredPermissions.every(permission => hasPermission(permission));

  // If user doesn't have access, show fallback or nothing
  if (!hasAllowedRole || !hasRequiredPermissions) {
    if (fallback) {
      return <>{fallback}</>;
    }

    if (showFallback) {
      return (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-yellow-700">
              <AlertCircle className="h-5 w-5" />
              Access Restricted
            </CardTitle>
            <CardDescription className="text-yellow-600">
              You don't have the required permissions to view this content.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm text-yellow-700">
              {allowedRoles.length > 0 && (
                <p>
                  <strong>Required roles:</strong> {allowedRoles.join(', ')}
                </p>
              )}
              {requiredPermissions.length > 0 && (
                <p>
                  <strong>Required permissions:</strong> {requiredPermissions.join(', ')}
                </p>
              )}
              <p>
                <strong>Your role:</strong> {user?.role || 'No role assigned'}
              </p>
            </div>
          </CardContent>
        </Card>
      );
    }

    return null;
  }

  // User has access, render children
  return <>{children}</>;
};

// Convenience components for common role checks
export const SuperAdminOnly = ({ children, fallback, showFallback = true }: Omit<RoleBasedAccessProps, 'allowedRoles'>) => (
  <RoleBasedAccess allowedRoles={['super_admin']} fallback={fallback} showFallback={showFallback}>
    {children}
  </RoleBasedAccess>
);

export const AdminOnly = ({ children, fallback, showFallback = true }: Omit<RoleBasedAccessProps, 'allowedRoles'>) => (
  <RoleBasedAccess allowedRoles={['super_admin', 'resort_admin']} fallback={fallback} showFallback={showFallback}>
    {children}
  </RoleBasedAccess>
);

export const AuthenticatedOnly = ({ children, fallback, showFallback = true }: Omit<RoleBasedAccessProps, 'allowedRoles'>) => (
  <RoleBasedAccess allowedRoles={['super_admin', 'resort_admin', 'viewer']} fallback={fallback} showFallback={showFallback}>
    {children}
  </RoleBasedAccess>
);

export default RoleBasedAccess;
