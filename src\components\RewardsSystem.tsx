
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Award,
  Gift,
  Users,
  Trophy,
  Star,
  Plus,
  Edit,
  Trash2,
  Timer,
  CheckCircle,
  Target,
  UserCheck
} from "lucide-react";
import { toast } from "sonner";

const RewardsSystem = () => {
  const [selectedRule, setSelectedRule] = useState(null);
  const [isRuleDialogOpen, setIsRuleDialogOpen] = useState(false);
  const [isNewRule, setIsNewRule] = useState(false);
  const [selectedAgents, setSelectedAgents] = useState([]);

  const rewardRules = [
    {
      id: "rule001",
      name: "First Referral Bonus",
      description: "Reward for sending first referral",
      trigger: "referral_sent",
      threshold: 1,
      rewardType: "points",
      rewardValue: 50,
      status: "Active",
      totalClaimed: 234,
      assignedAgents: ["vikas123", "maria456"]
    },
    {
      id: "rule002",
      name: "Triple Threat",
      description: "Complete 3 referrals in a week",
      trigger: "referral_completed",
      threshold: 3,
      rewardType: "badge",
      rewardValue: "Triple Threat",
      status: "Active",
      totalClaimed: 89,
      assignedAgents: ["vikas123"]
    },
    {
      id: "rule003",
      name: "Conversion Master",
      description: "5 successful conversions",
      trigger: "conversion",
      threshold: 5,
      rewardType: "gift",
      rewardValue: "Free Dinner Voucher",
      status: "Active",
      totalClaimed: 45,
      assignedAgents: ["maria456", "john789"]
    }
  ];

  const agents = [
    { id: "vikas123", name: "Vikas Patel", resort: "Cabo Dreams Resort" },
    { id: "maria456", name: "Maria Rodriguez", resort: "Riviera Paradise" },
    { id: "john789", name: "John Smith", resort: "Paradise Bay Resort" }
  ];

  const guestRewards = [
    {
      guestId: "guest001",
      guestName: "John Smith",
      totalPoints: 250,
      badges: ["First Timer", "Triple Threat"],
      lastReward: "2024-07-08",
      status: "Active",
      claimedRewards: [
        { type: "points", value: 50, date: "2024-07-05", rule: "First Referral Bonus" },
        { type: "badge", value: "Triple Threat", date: "2024-07-07", rule: "Triple Threat" },
        { type: "points", value: 150, date: "2024-07-08", rule: "Referral Streak" }
      ]
    },
    {
      guestId: "guest002",
      guestName: "Maria Garcia",
      totalPoints: 400,
      badges: ["Triple Threat", "Conversion Master"],
      lastReward: "2024-07-07",
      status: "VIP",
      claimedRewards: [
        { type: "gift", value: "Free Dinner Voucher", date: "2024-07-06", rule: "Conversion Master" },
        { type: "points", value: 300, date: "2024-07-07", rule: "Power Referrer" }
      ]
    }
  ];

  const handleAddRule = () => {
    setSelectedRule({
      name: "",
      description: "",
      trigger: "referral_sent",
      threshold: 1,
      rewardType: "points",
      rewardValue: "",
      status: "Active",
      assignedAgents: []
    });
    setSelectedAgents([]);
    setIsNewRule(true);
    setIsRuleDialogOpen(true);
  };

  const handleEditRule = (rule) => {
    setSelectedRule(rule);
    setSelectedAgents(rule.assignedAgents || []);
    setIsNewRule(false);
    setIsRuleDialogOpen(true);
  };

  const handleSaveRule = () => {
    if (isNewRule) {
      toast.success("New reward rule created and assigned successfully!");
    } else {
      toast.success("Reward rule updated successfully!");
    }
    setIsRuleDialogOpen(false);
  };

  const handleAgentToggle = (agentId, checked) => {
    if (checked) {
      setSelectedAgents([...selectedAgents, agentId]);
    } else {
      setSelectedAgents(selectedAgents.filter(id => id !== agentId));
    }
  };

  const getRewardTypeIcon = (type) => {
    const icons = {
      points: Star,
      badge: Award,
      gift: Gift
    };
    return icons[type] || Star;
  };

  const getStatusBadge = (status) => {
    const colors = {
      Active: "bg-green-100 text-green-700 border-green-200",
      Inactive: "bg-gray-100 text-gray-700 border-gray-200",
      VIP: "bg-purple-100 text-purple-700 border-purple-200"
    };
    return colors[status] || colors.Active;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-slate-800">Rewards System</h2>
          <p className="text-slate-600 mt-1">Manage referral rewards and gamification</p>
        </div>
        <Button onClick={handleAddRule} className="bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90">
          <Plus className="w-4 h-4 mr-2" />
          Add Reward Rule
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Active Rewards</p>
                <p className="text-2xl font-bold text-slate-800">{rewardRules.filter(r => r.status === 'Active').length}</p>
              </div>
              <Target className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Total Claims</p>
                <p className="text-2xl font-bold text-slate-800">
                  {rewardRules.reduce((sum, r) => sum + r.totalClaimed, 0)}
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        {/* <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">VIP Guests</p>
                <p className="text-2xl font-bold text-slate-800">
                  {guestRewards.filter(g => g.status === 'VIP').length}
                </p>
              </div>
              <Trophy className="w-8 h-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card> */}

        <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Points Awarded</p>
                <p className="text-2xl font-bold text-slate-800">
                  {guestRewards.reduce((sum, g) => sum + g.totalPoints, 0)}
                </p>
              </div>
              <Star className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="rules" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="rules">Reward Rules</TabsTrigger>
          <TabsTrigger value="assignments">Guest Rewards</TabsTrigger>
        </TabsList>

        <TabsContent value="rules" className="space-y-6">
          {/* Reward Rules */}
          <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
            <CardHeader>
              <CardTitle>Reward Rules</CardTitle>
              <CardDescription>Configure automatic rewards for referral milestones</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Rule Details</TableHead>
                    <TableHead>Trigger</TableHead>
                    <TableHead>Reward</TableHead>
                    <TableHead>Assigned Agents</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Claims</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {rewardRules.map((rule) => {
                    const RewardIcon = getRewardTypeIcon(rule.rewardType);
                    
                    return (
                      <TableRow key={rule.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium text-slate-800">{rule.name}</div>
                            <div className="text-sm text-slate-600">{rule.description}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="text-sm font-medium text-slate-800">
                              {rule.trigger.replace('_', ' ').toUpperCase()}
                            </div>
                            <div className="text-xs text-slate-500">
                              Threshold: {rule.threshold}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <RewardIcon className="w-4 h-4 text-slate-500" />
                            <div>
                              <div className="text-sm font-medium text-slate-800">
                                {rule.rewardValue}
                              </div>
                              <div className="text-xs text-slate-500 capitalize">
                                {rule.rewardType}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm text-slate-600">
                            {rule.assignedAgents?.length || 0} agents
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusBadge(rule.status)}>
                            {rule.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="text-center">
                            <div className="text-lg font-bold text-slate-800">{rule.totalClaimed}</div>
                            <div className="text-xs text-slate-500">total</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEditRule(rule)}
                            >
                              <Edit className="w-3 h-3" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="assignments" className="space-y-6">
          {/* Guest Rewards */}
          <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
            <CardHeader>
              <CardTitle>Guest Rewards Status</CardTitle>
              <CardDescription>Track individual guest reward progress</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                {guestRewards.map((guest) => (
                  <div key={guest.guestId} className="p-4 border border-slate-200 rounded-lg bg-slate-50">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-gradient-to-r from-primary to-secondary text-white rounded-lg">
                          <Users className="w-4 h-4" />
                        </div>
                        <div>
                          <div className="font-semibold text-slate-800">{guest.guestName}</div>
                          <div className="text-sm text-slate-600">ID: {guest.guestId}</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge className={getStatusBadge(guest.status)}>
                          {guest.status}
                        </Badge>
                        <div className="text-sm text-slate-600 mt-1">
                          Last reward: {guest.lastReward}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-6">
                        <div className="flex items-center space-x-2">
                          <Star className="w-4 h-4 text-yellow-500" />
                          <span className="font-medium text-slate-800">{guest.totalPoints}</span>
                          <span className="text-sm text-slate-600">points</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Award className="w-4 h-4 text-purple-500" />
                          <span className="font-medium text-slate-800">{guest.badges.length}</span>
                          <span className="text-sm text-slate-600">badges</span>
                        </div>
                      </div>
                      
                      <div className="flex flex-wrap gap-1">
                        {guest.badges.map((badge, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {badge}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Add/Edit Rule Dialog */}
      <Dialog open={isRuleDialogOpen} onOpenChange={setIsRuleDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{isNewRule ? 'Add New Reward Rule' : 'Edit Reward Rule'}</DialogTitle>
            <DialogDescription>
              Configure the trigger conditions, rewards, and agent assignments for this rule
            </DialogDescription>
          </DialogHeader>
          
          {selectedRule && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="ruleName">Rule Name</Label>
                  <Input
                    id="ruleName"
                    value={selectedRule.name}
                    onChange={(e) => setSelectedRule({...selectedRule, name: e.target.value})}
                    placeholder="Enter rule name"
                  />
                </div>

                <div>
                  <Label htmlFor="status">Status</Label>
                  <Select 
                    value={selectedRule.status} 
                    onValueChange={(value) => setSelectedRule({...selectedRule, status: value})}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Active">Active</SelectItem>
                      <SelectItem value="Inactive">Inactive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={selectedRule.description}
                  onChange={(e) => setSelectedRule({...selectedRule, description: e.target.value})}
                  placeholder="Describe what this rule does"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="trigger">Trigger</Label>
                  <Select 
                    value={selectedRule.trigger} 
                    onValueChange={(value) => setSelectedRule({...selectedRule, trigger: value})}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="referral_sent">Referral Sent</SelectItem>
                      <SelectItem value="referral_completed">Referral Completed</SelectItem>
                      <SelectItem value="conversion">Conversion</SelectItem>
                      <SelectItem value="streak">Streak</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="threshold">Threshold</Label>
                  <Input
                    id="threshold"
                    type="number"
                    value={selectedRule.threshold}
                    onChange={(e) => setSelectedRule({...selectedRule, threshold: parseInt(e.target.value)})}
                    min="1"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="rewardType">Reward Type</Label>
                  <Select 
                    value={selectedRule.rewardType} 
                    onValueChange={(value) => setSelectedRule({...selectedRule, rewardType: value})}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="points">Points</SelectItem>
                      <SelectItem value="badge">Badge</SelectItem>
                      <SelectItem value="gift">Gift/Voucher</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="rewardValue">Reward Value</Label>
                  <Input
                    id="rewardValue"
                    value={selectedRule.rewardValue}
                    onChange={(e) => setSelectedRule({...selectedRule, rewardValue: e.target.value})}
                    placeholder={selectedRule.rewardType === 'points' ? '100' : 'Badge/Gift name'}
                  />
                </div>
              </div>

              <div>
                <Label className="text-base font-medium">Assign to Agents</Label>
                <div className="mt-2 space-y-2 max-h-32 overflow-y-auto">
                  {agents.map((agent) => (
                    <div key={agent.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={agent.id}
                        checked={selectedAgents.includes(agent.id)}
                        onCheckedChange={(checked) => handleAgentToggle(agent.id, checked)}
                      />
                      <Label htmlFor={agent.id} className="flex-1 cursor-pointer">
                        <div className="flex items-center justify-between">
                          <span className="font-medium">{agent.name}</span>
                          <span className="text-sm text-slate-500">{agent.resort}</span>
                        </div>
                      </Label>
                    </div>
                  ))}
                </div>
                <p className="text-sm text-slate-500 mt-2">
                  Selected: {selectedAgents.length} agents
                </p>
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <Button variant="outline" onClick={() => setIsRuleDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleSaveRule}>
                  {isNewRule ? 'Create Rule' : 'Save Changes'}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default RewardsSystem;
