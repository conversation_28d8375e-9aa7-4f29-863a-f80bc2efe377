
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Users,
  Search,
  Filter,
  Download,
  Upload,
  Phone,
  Mail,
  Calendar,
  MapPin,
  Zap,
  Smartphone,
  Globe
} from "lucide-react";
import { toast } from "sonner";

const ContactsManager = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [selectedContact, setSelectedContact] = useState(null);
  const [isDetailOpen, setIsDetailOpen] = useState(false);

  const contacts = [
    {
      id: "cont001",
      name: "Sarah Johnson",
      phone: "+****************",
      email: "<EMAIL>",
      source: "Sales RepresentativeSync",
      agentName: "Vikas Patel",
      syncDate: "2024-07-08",
      status: "Active",
      location: "Miami, FL",
      lastContact: "2024-07-05",
      campaigns: 3,
      tags: ["VIP", "Resort Guest"]
    },
    {
      id: "cont002",
      name: "Michael Chen",
      phone: "+****************",
      email: "<EMAIL>",
      source: "Manual Import",
      agentName: "Maria Rodriguez",
      syncDate: "2024-07-07",
      status: "Pending",
      location: "Los Angeles, CA",
      lastContact: "2024-07-03",
      campaigns: 1,
      tags: ["Lead"]
    },
    {
      id: "cont003",
      name: "Emma Williams",
      phone: "+****************",
      email: "<EMAIL>",
      source: "Sales RepresentativeSync",
      agentName: "Vikas Patel",
      syncDate: "2024-07-06",
      status: "Active",
      location: "New York, NY",
      lastContact: "2024-07-08",
      campaigns: 5,
      tags: ["High Value", "Repeat Customer"]
    }
  ];

  const handleSyncGHL = () => {
    toast.success("GHL CRM sync initiated successfully!");
  };

  const handleExportContacts = () => {
    toast.success("Contacts exported successfully!");
  };

  const handleViewContact = (contact) => {
    setSelectedContact(contact);
    setIsDetailOpen(true);
  };

  const getStatusBadge = (status) => {
    const colors = {
      Active: "bg-green-100 text-green-700 border-green-200",
      Pending: "bg-yellow-100 text-yellow-700 border-yellow-200",
      Inactive: "bg-gray-100 text-gray-700 border-gray-200"
    };
    return colors[status] || colors.Active;
  };

  const filteredContacts = contacts.filter(contact => {
    const matchesSearch = contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         contact.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         contact.phone.includes(searchTerm);
    const matchesStatus = statusFilter === "all" || contact.status.toLowerCase() === statusFilter;
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-slate-800">Contact Management</h2>
          <p className="text-slate-600 mt-1">Manage synced contacts and CRM integrations</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleExportContacts}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button onClick={handleSyncGHL} className="bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90">
            <Zap className="w-4 h-4 mr-2" />
            Sync to GHL
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Total Contacts</p>
                <p className="text-2xl font-bold text-slate-800">11,000</p>
              </div>
              <Users className="w-8 h-8 text-primary" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Active Contacts</p>
                <p className="text-2xl font-bold text-slate-800">
                  {/* {contacts.filter(c => c.status === 'Active').length} */}
                  4000
                </p>
              </div>
              <Phone className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">In Campaigns</p>
                <p className="text-2xl font-bold text-slate-800">
                  {/* {contacts.reduce((sum, c) => sum + c.campaigns, 0)} */}
                  2000
                </p>
              </div>
              <Mail className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Last Sync</p>
                <p className="text-2xl font-bold text-slate-800">Today</p>
              </div>
              <Smartphone className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
        <CardContent className="p-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[300px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                <Input
                  placeholder="Search by name, email, or phone..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Contacts Table */}
      <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
        <CardHeader>
          <CardTitle>Synced Contacts</CardTitle>
          <CardDescription>Contacts imported from agent mobile applications</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Contact Info</TableHead>
                <TableHead>Collected Contact Count</TableHead>
                <TableHead>Status</TableHead>
                {/* <TableHead>Location</TableHead> */}
                <TableHead>Campaigns</TableHead>
                <TableHead>Last Contact</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredContacts.map((contact) => (
                <TableRow key={contact.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium text-slate-800">{contact.name}</div>
                      <div className="text-sm text-slate-600">{contact.email}</div>
                      <div className="text-sm text-slate-500">{contact.phone}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="text-sm font-medium text-slate-800">6000</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={getStatusBadge(contact.status)}>
                      {contact.status}
                    </Badge>
                  </TableCell>
                  {/* <TableCell>
                    <div className="flex items-center text-sm text-slate-600">
                      <MapPin className="w-3 h-3 mr-1" />
                      {contact.location}
                    </div>
                  </TableCell> */}
                  <TableCell>
                    <div className="text-center">
                      <div className="text-lg font-bold text-slate-800">{contact.campaigns}</div>
                      <div className="text-xs text-slate-500">active</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm text-slate-600">{contact.lastContact}</div>
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewContact(contact)}
                    >
                      View Details
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Contact Detail Dialog */}
      <Dialog open={isDetailOpen} onOpenChange={setIsDetailOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Contact Details</DialogTitle>
            <DialogDescription>
              Detailed information about {selectedContact?.name}
            </DialogDescription>
          </DialogHeader>
          
          {selectedContact && (
            <div className="space-y-4">
              <div>
                <Label className="text-sm font-medium text-slate-600">Name</Label>
                <p className="text-slate-800 font-medium">{selectedContact.name}</p>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-slate-600">Phone</Label>
                  <p className="text-slate-800">{selectedContact.phone}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-slate-600">Email</Label>
                  <p className="text-slate-800">{selectedContact.email}</p>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium text-slate-600">Tags</Label>
                <div className="flex flex-wrap gap-1 mt-1">
                  {selectedContact.tags?.map((tag, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-slate-600">Location</Label>
                  <p className="text-slate-800">{selectedContact.location}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-slate-600">Status</Label>
                  <Badge className={getStatusBadge(selectedContact.status)}>
                    {selectedContact.status}
                  </Badge>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ContactsManager;
