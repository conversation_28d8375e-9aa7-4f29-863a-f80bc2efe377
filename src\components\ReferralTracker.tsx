
import { useState, useEffect } from "react";

// Redux imports
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { fetchReferrals } from "@/redux/actions/referralActions";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Share2,
  Search,
  Filter,
  Download,
  ExternalLink,
  Calendar,
  MapPin,
  User,
  Phone,
  Mail,
  MessageSquare,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
  Brain,
  TrendingUp,
  AlertTriangle,
  Target,
  Zap
} from "lucide-react";
import { toast } from "sonner";

const ReferralTracker = () => {
  // Redux state and dispatch
  const dispatch = useAppDispatch();
  const { referrals, loading, error } = useAppSelector((state) => state.referrals);

  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [platformFilter, setPlatformFilter] = useState("all");
  const [selectedReferral, setSelectedReferral] = useState(null);
  const [isDetailOpen, setIsDetailOpen] = useState(false);

  // Initialize referrals data
  useEffect(() => {
    dispatch(fetchReferrals() as any);
  }, [dispatch]);

  // Use referrals from Redux state instead of local dummy data

  const filteredReferrals = referrals.filter(referral => {
    const matchesSearch =
      referral.referrerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      referral.refereeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      referral.refereeEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
      referral.resort.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === "all" || referral.status.toLowerCase() === statusFilter;
    // Note: platform filtering would need to be added to the referral data structure

    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      "completed": { variant: "default", color: "bg-green-100 text-green-700 border-green-200" },
      "pending": { variant: "secondary", color: "bg-blue-100 text-blue-700 border-blue-200" },
      "cancelled": { variant: "destructive", color: "bg-red-100 text-red-700 border-red-200" }
    };

    return statusConfig[status.toLowerCase()] || statusConfig["pending"];
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading referrals...</div>
      </div>
    );
  }

  const getPlatformIcon = (platform) => {
    const icons = {
      "WhatsApp": MessageSquare,
      "SMS": Phone,
      "Email": Mail
    };
    return icons[platform] || MessageSquare;
  };

  const copyReferralLink = (link) => {
    navigator.clipboard.writeText(link);
    toast.success("Referral link copied to clipboard!");
  };

  const exportData = () => {
    toast.success("Referral data exported successfully!");
  };

  const handleReferralClick = (referral) => {
    setSelectedReferral(referral);
    setIsDetailOpen(true);
  };

  // AI-generated insights
  const aiInsights = [
    {
      type: "trending",
      icon: TrendingUp,
      title: "Peak Performance Time",
      description: "WhatsApp referrals sent between 7-9 PM show 45% higher conversion rates",
      color: "text-green-600"
    },
    {
      type: "alert",
      icon: AlertTriangle,
      title: "Platform Optimization",
      description: "Email referrals are underperforming. Consider A/B testing subject lines",
      color: "text-orange-600"
    },
    {
      type: "target",
      icon: Target,
      title: "High-Value Opportunity",
      description: "Cabo Dreams Resort guests show 78% completion rate - priority targeting",
      color: "text-blue-600"
    },
    {
      type: "automation",
      icon: Zap,
      title: "Automation Suggestion",
      description: "Set up 24-hour follow-up SMS for unclicked email referrals",
      color: "text-purple-600"
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-slate-800">Referral Tracking</h2>
          <p className="text-slate-600 mt-1">Monitor referral performance and conversion rates</p>
        </div>
        <Button onClick={exportData} className="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600">
          <Download className="w-4 h-4 mr-2" />
          Export Data
        </Button>
      </div>

      {/* AI Insights */}
      <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Brain className="w-5 h-5 mr-2 text-purple-600" />
            AI-Powered Insights
          </CardTitle>
          <CardDescription>Automated analysis and recommendations</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {aiInsights.map((insight, index) => (
              <div key={index} className="p-3 bg-white rounded-lg border border-purple-200">
                <div className="flex items-start space-x-2">
                  <insight.icon className={`w-4 h-4 mt-0.5 ${insight.color}`} />
                  <div>
                    <span className="font-semibold text-slate-800">{insight.title}</span>
                    <p className="text-sm text-slate-600 mt-1">{insight.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Total Referrals</p>
                <p className="text-2xl font-bold text-slate-800">{referrals.length}</p>
              </div>
              <Share2 className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Completed</p>
                <p className="text-2xl font-bold text-slate-800">
                  {referrals.filter(r => r.status === 'Completed').length}
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Click Rate</p>
                <p className="text-2xl font-bold text-slate-800">
                  {Math.round((referrals.filter(r => r.clicked).length / referrals.length) * 100)}%
                </p>
              </div>
              <Eye className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Conversion Rate</p>
                <p className="text-2xl font-bold text-slate-800">
                  {Math.round((referrals.filter(r => r.converted).length / referrals.length) * 100)}%
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
        <CardContent className="p-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[300px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                <Input
                  placeholder="Search by guest, contact, agent, or resort..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="clicked">Clicked</SelectItem>
                <SelectItem value="sent">Sent</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
              </SelectContent>
            </Select>
            <Select value={platformFilter} onValueChange={setPlatformFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Platform" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Platforms</SelectItem>
                <SelectItem value="whatsapp">WhatsApp</SelectItem>
                <SelectItem value="sms">SMS</SelectItem>
                <SelectItem value="email">Email</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Referrals Table */}
      <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
        <CardHeader>
          <CardTitle>Referral Activity Log</CardTitle>
          <CardDescription>Detailed tracking of all referral activities with AI insights</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Contact Details</TableHead>
                <TableHead>Referred By</TableHead>
                <TableHead>Sales Reps</TableHead>
                <TableHead>Platform</TableHead>
                <TableHead>Status & Timeline</TableHead>
                <TableHead>AI Insight</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredReferrals.map((referral) => {
                const PlatformIcon = getPlatformIcon(referral.platform);
                const statusConfig = getStatusBadge(referral.status);
                
                return (
                  <TableRow key={referral.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium text-slate-800">{referral.guestName}</div>
                        <div className="text-sm text-slate-600">ID: {referral.id}</div>
                        <div className="text-xs text-slate-500 flex items-center mt-1">
                          <Calendar className="w-3 h-3 mr-1" />
                          {referral.sentDate}
                        </div>
                      </div>
                    </TableCell>
                   
                    <TableCell>
                      <div>
                        <div className="font-medium text-slate-800">{referral.contactName}</div>
                        <div className="text-sm text-slate-600">{referral.contactPhone}</div>
                        <div className="text-xs text-slate-500">{referral.contactEmail}</div>
                      </div>
                    </TableCell>
                     <TableCell>
                      <div>
                        <div className="font-medium text-slate-800">{referral.agentName}</div>
                        <div className="text-sm text-slate-600 flex items-center">
                          {/* <MapPin className="w-3 h-3 mr-1" />
                          {referral.resort} */}
                          SR-ID: SR122
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <PlatformIcon className="w-4 h-4 mr-2 text-slate-500" />
                        {referral.platform}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={statusConfig.color}>
                        {referral.status}
                      </Badge>
                      <div className="flex space-x-1 mt-1">
                        {/* {referral.clicked && (
                          <Badge variant="outline" className="text-xs">
                            <Eye className="w-2 h-2 mr-1" />
                            Clicked
                          </Badge>
                        )} */}
                        {/* {referral.converted && (
                          <Badge variant="outline" className="text-xs">
                            <CheckCircle className="w-2 h-2 mr-1" />
                            Converted
                          </Badge>
                        )} */}
                      </div>
                      {referral.clickedDate && (
                        <div className="text-xs text-slate-500 mt-1">
                          Clicked: {referral.clickedDate}
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="max-w-xs">
                      <div className="text-xs text-slate-600 bg-slate-50 p-2 rounded border">
                        <Brain className="w-3 h-3 inline mr-1 text-purple-500" />
                        {referral.aiInsight}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => copyReferralLink(referral.referralLink)}
                        >
                          <ExternalLink className="w-3 h-3" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleReferralClick(referral)}
                        >
                          <Eye className="w-3 h-3" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Referral Detail Dialog */}
      <Dialog open={isDetailOpen} onOpenChange={setIsDetailOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Referral Details</DialogTitle>
            <DialogDescription>
              {selectedReferral?.id ? `Complete tracking information for ${selectedReferral.id}` : 'Referral information'}
            </DialogDescription>
          </DialogHeader>
          
          {selectedReferral && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold text-slate-800 mb-2">Referral Chain</h4>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <User className="w-4 h-4 text-blue-500" />
                      <span className="text-sm">Agent: {selectedReferral.agentName}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <User className="w-4 h-4 text-green-500" />
                      <span className="text-sm">Guest: {selectedReferral.guestName}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <User className="w-4 h-4 text-purple-500" />
                      <span className="text-sm">Referee: {selectedReferral.contactName}</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold text-slate-800 mb-2">Timeline</h4>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Clock className="w-4 h-4 text-slate-500" />
                      <span className="text-sm">Sent: {selectedReferral.sentDate}</span>
                    </div>
                    {selectedReferral.clickedDate && (
                      <div className="flex items-center space-x-2">
                        <Eye className="w-4 h-4 text-blue-500" />
                        <span className="text-sm">Clicked: {selectedReferral.clickedDate}</span>
                      </div>
                    )}
                    {selectedReferral.completedDate && (
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        <span className="text-sm">Completed: {selectedReferral.completedDate}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-semibold text-slate-800 mb-2">AI Analysis</h4>
                <div className="p-3 bg-purple-50 rounded-lg border border-purple-200">
                  <div className="flex items-start space-x-2">
                    <Brain className="w-4 h-4 text-purple-600 mt-0.5" />
                    <p className="text-sm text-slate-700">{selectedReferral.aiInsight}</p>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-semibold text-slate-800 mb-2">Referral Link</h4>
                <div className="flex items-center space-x-2">
                  <Input 
                    value={selectedReferral.referralLink} 
                    readOnly 
                    className="flex-1"
                  />
                  <Button 
                    variant="outline" 
                    onClick={() => copyReferralLink(selectedReferral.referralLink)}
                  >
                    Copy
                  </Button>
                </div>
              </div>

              <div className="flex justify-end space-x-2 pt-4 border-t">
                <Button variant="outline" onClick={() => setIsDetailOpen(false)}>
                  Close
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ReferralTracker;
