// Base interfaces
export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
}

// Agent Types
export interface Agent extends BaseEntity {
  name: string;
  email: string;
  phone: string;
  role: string;
  status: 'active' | 'inactive' | 'pending';
  avatar?: string;
  resort: string;
  totalReferrals: number;
  completedReferrals: number;
  revenue: number;
  commission: number;
  joinDate: string;
  lastActive: string;
  qrCode?: string;
  referralCode: string;
}

// Guest Types
export interface Guest extends BaseEntity {
  name: string;
  email: string;
  phone: string;
  status: 'active' | 'inactive' | 'pending';
  checkInDate?: string;
  checkOutDate?: string;
  resort: string;
  roomNumber?: string;
  referredBy?: string;
  totalSpent: number;
  visits: number;
}

// Referral Types
export interface Referral extends BaseEntity {
  referrerName: string;
  referrerEmail: string;
  refereeName: string;
  refereeEmail: string;
  refereePhone: string;
  status: 'pending' | 'completed' | 'cancelled';
  reward: number;
  commission: number;
  completedDate?: string;
  resort: string;
  notes?: string;
}

// Contact Types
export interface Contact extends BaseEntity {
  name: string;
  email: string;
  phone: string;
  type: 'lead' | 'customer' | 'agent' | 'partner';
  status: 'active' | 'inactive';
  source: string;
  tags: string[];
  notes?: string;
  lastContact?: string;
}

// Notification Types
export interface Notification extends BaseEntity {
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  isRead: boolean;
  userId: string;
  actionUrl?: string;
  priority: 'low' | 'medium' | 'high';
}

// Reward Types
export interface Reward extends BaseEntity {
  title: string;
  description: string;
  type: 'points' | 'cash' | 'discount' | 'gift';
  value: number;
  pointsRequired: number;
  isActive: boolean;
  expiryDate?: string;
  imageUrl?: string;
  termsAndConditions: string;
  claimedCount: number;
  maxClaims?: number;
}

// User Profile Types
export interface UserProfile extends BaseEntity {
  name: string;
  email: string;
  phone?: string;
  avatar?: string;
  role: string;
  resort?: string;
  preferences: {
    notifications: boolean;
    emailUpdates: boolean;
    theme: 'light' | 'dark' | 'system';
  };
  stats: {
    totalReferrals: number;
    completedReferrals: number;
    totalEarnings: number;
    currentPoints: number;
  };
}

// Dashboard Types
export interface DashboardStats {
  totalReferrals: number;
  completedReferrals: number;
  pendingReferrals: number;
  totalRevenue: number;
  totalCommission: number;
  activeAgents: number;
  activeGuests: number;
  conversionRate: number;
}

export interface ChartData {
  name: string;
  referrals: number;
  completed: number;
  revenue?: number;
}

// Analytics Types
export interface AnalyticsData {
  overview: DashboardStats;
  chartData: ChartData[];
  topPerformers: Agent[];
  recentActivity: any[];
  conversionFunnel: {
    stage: string;
    count: number;
    percentage: number;
  }[];
}

// Integration Types
export interface Integration extends BaseEntity {
  name: string;
  type: 'email' | 'sms' | 'crm' | 'payment' | 'analytics';
  status: 'connected' | 'disconnected' | 'error';
  config: Record<string, any>;
  lastSync?: string;
  isEnabled: boolean;
}

// Referral Tree Types
export interface TreeNode {
  id: string;
  name: string;
  email: string;
  level: number;
  children: TreeNode[];
  isExpanded: boolean;
  referralCount: number;
  totalEarnings: number;
}

// Filter Types
export interface DateRange {
  from: Date;
  to: Date;
}

export interface DashboardFilters {
  period: string;
  dateRange: DateRange;
  resort?: string;
  agent?: string;
}

// State Types
export interface AgentsState {
  agents: Agent[];
  selectedAgent: Agent | null;
  loading: boolean;
  error: string | null;
}

export interface GuestsState {
  guests: Guest[];
  loading: boolean;
  error: string | null;
}

export interface ReferralsState {
  referrals: Referral[];
  loading: boolean;
  error: string | null;
}

export interface ContactsState {
  contacts: Contact[];
  loading: boolean;
  error: string | null;
}

export interface NotificationsState {
  notifications: Notification[];
  unreadCount: number;
  loading: boolean;
  error: string | null;
}

export interface RewardsState {
  rewards: Reward[];
  userPoints: number;
  claimedRewards: string[];
  loading: boolean;
  error: string | null;
}

export interface AnalyticsState {
  data: AnalyticsData | null;
  filters: DashboardFilters;
  loading: boolean;
  error: string | null;
}

export interface DashboardState {
  stats: DashboardStats | null;
  chartData: ChartData[];
  filters: DashboardFilters;
  loading: boolean;
  error: string | null;
}

export interface ReferralTreeState {
  treeData: TreeNode[];
  expandedNodes: string[];
  loading: boolean;
  error: string | null;
}

export interface IntegrationsState {
  integrations: Integration[];
  loading: boolean;
  error: string | null;
}

export interface UserProfileState {
  profile: UserProfile | null;
  loading: boolean;
  error: string | null;
}

export interface UIState {
  activeTab: string;
  isSidebarOpen: boolean;
  loading: boolean;
  error: string | null;
}

// Root State
export interface RootState {
  agents: AgentsState;
  guests: GuestsState;
  referrals: ReferralsState;
  contacts: ContactsState;
  notifications: NotificationsState;
  rewards: RewardsState;
  analytics: AnalyticsState;
  dashboard: DashboardState;
  referralTree: ReferralTreeState;
  integrations: IntegrationsState;
  userProfile: UserProfileState;
  ui: UIState;
}

// Action Types
export interface Action {
  type: string;
  payload?: any;
}

export interface AsyncAction {
  type: string;
  payload?: any;
  error?: string;
}
