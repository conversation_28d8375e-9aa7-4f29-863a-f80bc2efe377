
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  GitBranch,
  Search,
  Filter,
  Eye,
  Users,
  Share2,
  Trophy,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  MapPin,
  Calendar,
  ChevronDown,
  ChevronRight,
  User,
  UserPlus
} from "lucide-react";

const ReferralTree = () => {
  const [selectedAgent, setSelectedAgent] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedNode, setSelectedNode] = useState(null);
  const [isDetailOpen, setIsDetailOpen] = useState(false);
  const [expandedBranches, setExpandedBranches] = useState(new Set(["guest001", "guest002"]));

  const referralTrees = [
    {
      agent: {
        id: "vikas123",
        name: "Vikas Patel",
        acronym: "VP",
        resort: "Cabo Dreams Resort",
        totalReferrals: 156,
        completionRate: 68
      },
      branches: [
        {
          guest: {
            id: "guest001",
            name: "John Smith",
            acronym: "JS",
            joinDate: "2024-07-05",
            status: "Active"
          },
          referrals: [
            { id: "ref001", name: "Sarah Johnson", acronym: "SJ", status: "Completed", date: "2024-07-07", platform: "WhatsApp" },
            { id: "ref002", name: "Mike Wilson", acronym: "MW", status: "Clicked", date: "2024-07-08", platform: "SMS" },
            { id: "ref003", name: "Lisa Chen", acronym: "LC", status: "Sent", date: "2024-07-08", platform: "Email" }
          ]
        },
        {
          guest: {
            id: "guest002",
            name: "Maria Garcia",
            acronym: "MG",
            joinDate: "2024-07-03",
            status: "Active"
          },
          referrals: [
            { id: "ref004", name: "Carlos Rivera", acronym: "CR", status: "Completed", date: "2024-07-06", platform: "WhatsApp" },
            { id: "ref005", name: "Emma Thompson", acronym: "ET", status: "Completed", date: "2024-07-07", platform: "WhatsApp" },
            { id: "ref006", name: "David Brown", acronym: "DB", status: "Clicked", date: "2024-07-07", platform: "SMS" },
            { id: "ref007", name: "Anna Wilson", acronym: "AW", status: "Failed", date: "2024-07-05", platform: "Email" },
            { id: "ref008", name: "Tom Davis", acronym: "TD", status: "Sent", date: "2024-07-08", platform: "WhatsApp" }
          ]
        }
      ]
    }
  ];

  const toggleBranch = (branchId) => {
    const newExpanded = new Set(expandedBranches);
    if (newExpanded.has(branchId)) {
      newExpanded.delete(branchId);
    } else {
      newExpanded.add(branchId);
    }
    setExpandedBranches(newExpanded);
  };

  const getStatusColor = (status) => {
    const colors = {
      "Completed": "bg-green-500",
      "Clicked": "bg-blue-500",
      "Sent": "bg-yellow-500",
      "Failed": "bg-red-500"
    };
    return colors[status] || "bg-gray-500";
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      "Completed": { color: "bg-green-100 text-green-700 border-green-200", icon: CheckCircle },
      "Clicked": { color: "bg-blue-100 text-blue-700 border-blue-200", icon: Eye },
      "Sent": { color: "bg-yellow-100 text-yellow-700 border-yellow-200", icon: Share2 },
      "Failed": { color: "bg-red-100 text-red-700 border-red-200", icon: AlertTriangle }
    };
    return statusConfig[status] || statusConfig["Sent"];
  };

  const handleNodeClick = (node) => {
    setSelectedNode(node);
    setIsDetailOpen(true);
  };

  const filteredTrees = referralTrees.filter(tree => {
    if (selectedAgent !== "all" && tree.agent.id !== selectedAgent) return false;
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      return tree.agent.name.toLowerCase().includes(searchLower) ||
             tree.branches.some(branch => 
               branch.guest.name.toLowerCase().includes(searchLower) ||
               branch.referrals.some(ref => ref.name.toLowerCase().includes(searchLower))
             );
    }
    return true;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-slate-800">Referral Tree Visualization</h2>
          <p className="text-slate-600 mt-1">Interactive view of referral hierarchies and performance</p>
        </div>
      </div>

      {/* AI Insights for Trees */}
      <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
        <CardHeader>
          <CardTitle className="flex items-center">
            <TrendingUp className="w-5 h-5 mr-2 text-green-600" />
            Tree Analysis Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-3 bg-white rounded-lg border border-green-200">
              <div className="flex items-center space-x-2">
                <Trophy className="w-4 h-4 text-yellow-600" />
                <span className="font-semibold text-slate-800">Top Branch</span>
              </div>
              <p className="text-sm text-slate-600 mt-1">Maria Garcia's branch has 5 referrals with 40% completion rate</p>
            </div>
            <div className="p-3 bg-white rounded-lg border border-green-200">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="w-4 h-4 text-orange-600" />
                <span className="font-semibold text-slate-800">Bottleneck Alert</span>
              </div>
              <p className="text-sm text-slate-600 mt-1">David Wilson shows no activity for 4 days - needs re-engagement</p>
            </div>
            <div className="p-3 bg-white rounded-lg border border-green-200">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span className="font-semibold text-slate-800">High Performer</span>
              </div>
              <p className="text-sm text-slate-600 mt-1">Vikas Patel's tree shows consistent growth pattern</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
        <CardContent className="p-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[300px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                <Input
                  placeholder="Search agents, guests, or referees..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={selectedAgent} onValueChange={setSelectedAgent}>
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Select agent" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Agents</SelectItem>
                {referralTrees.map((tree) => (
                  <SelectItem key={tree.agent.id} value={tree.agent.id}>
                    {tree.agent.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Expandable Tree Visualization */}
      <div className="space-y-8">
        {filteredTrees.map((tree, treeIndex) => (
          <Card key={treeIndex} className="bg-white/70 backdrop-blur-sm border-slate-200">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-3 bg-gradient-to-r from-primary to-secondary text-white rounded-xl shadow-lg">
                    <div className="text-lg font-bold">{tree.agent.acronym}</div>
                  </div>
                  <div>
                    <CardTitle className="text-xl">{tree.agent.name}</CardTitle>
                    <CardDescription className="flex items-center">
                      <MapPin className="w-3 h-3 mr-1" />
                      {tree.agent.resort}
                    </CardDescription>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-slate-800">{tree.agent.totalReferrals}</div>
                  <div className="text-sm text-slate-600">referrals</div>
                  <Badge className="bg-green-100 text-green-700 border-green-200 mt-1">
                    {tree.agent.completionRate}% completion
                  </Badge>
                </div>
              </div>
            </CardHeader>
            
            <CardContent>
              <div className="space-y-4">
                {tree.branches.map((branch, branchIndex) => {
                  const isExpanded = expandedBranches.has(branch.guest.id);
                  
                  return (
                    <Collapsible 
                      key={branchIndex} 
                      open={isExpanded}
                      onOpenChange={() => toggleBranch(branch.guest.id)}
                    >
                      <div className="border border-slate-200 rounded-lg bg-slate-50">
                        {/* Guest Node Header */}
                        <CollapsibleTrigger asChild>
                          <div className="flex items-center justify-between p-4 cursor-pointer hover:bg-slate-100 transition-colors">
                            <div className="flex items-center space-x-3">
                              <div className="flex items-center space-x-2">
                                {isExpanded ? 
                                  <ChevronDown className="w-4 h-4 text-slate-500" /> : 
                                  <ChevronRight className="w-4 h-4 text-slate-500" />
                                }
                                <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg shadow">
                                  <div className="text-sm font-bold">{branch.guest.acronym}</div>
                                </div>
                              </div>
                              <div>
                                <div className="font-semibold text-slate-800">{branch.guest.name}</div>
                                <div className="text-sm text-slate-600 flex items-center">
                                  <Calendar className="w-3 h-3 mr-1" />
                                  Joined: {branch.guest.joinDate}
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center space-x-3">
                              <Badge className={branch.guest.status === 'Active' ? 
                                "bg-green-100 text-green-700 border-green-200" : 
                                "bg-gray-100 text-gray-700 border-gray-200"
                              }>
                                {branch.guest.status}
                              </Badge>
                              <div className="text-sm text-slate-600">
                                {branch.referrals.length} referrals
                              </div>
                            </div>
                          </div>
                        </CollapsibleTrigger>

                        {/* Referral Nodes */}
                        <CollapsibleContent>
                          <div className="px-4 pb-4">
                            <div className="ml-8 space-y-2">
                              {branch.referrals.map((referral, refIndex) => {
                                const statusConfig = getStatusBadge(referral.status);
                                const StatusIcon = statusConfig.icon;
                                
                                return (
                                  <div key={refIndex} className="flex items-center space-x-3 p-3 bg-white rounded-lg border border-slate-200 hover:shadow-md transition-all cursor-pointer group"
                                       onClick={() => handleNodeClick(referral)}>
                                    <div className="flex items-center space-x-3">
                                      <div className={`w-8 h-8 rounded-full ${getStatusColor(referral.status)} flex items-center justify-center text-white text-xs font-bold shadow`}>
                                        {referral.acronym}
                                      </div>
                                      <div className="w-6 h-px bg-slate-300"></div>
                                    </div>
                                    
                                    <div className="flex-1">
                                      <div className="flex items-center justify-between">
                                        <div>
                                          <div className="font-medium text-slate-800 group-hover:text-primary transition-colors">
                                            {referral.name}
                                          </div>
                                          <div className="text-xs text-slate-500 flex items-center space-x-2">
                                            <span className="flex items-center">
                                              <Share2 className="w-3 h-3 mr-1" />
                                              {referral.platform}
                                            </span>
                                            <span>•</span>
                                            <span>{referral.date}</span>
                                          </div>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                          <StatusIcon className="w-4 h-4 text-slate-500" />
                                          <Badge className={`${statusConfig.color} text-xs`}>
                                            {referral.status}
                                          </Badge>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        </CollapsibleContent>
                      </div>
                    </Collapsible>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Detail Dialog */}
      <Dialog open={isDetailOpen} onOpenChange={setIsDetailOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Node Details</DialogTitle>
            <DialogDescription>
              {selectedNode?.name ? `Details for ${selectedNode.name}` : 'Node information'}
            </DialogDescription>
          </DialogHeader>
          
          {selectedNode && (
            <div className="space-y-4">
              <div>
                <Label className="text-sm font-medium text-slate-600">Name</Label>
                <p className="text-slate-800">{selectedNode.name}</p>
              </div>
              
              {selectedNode.joinDate && (
                <div>
                  <Label className="text-sm font-medium text-slate-600">Join Date</Label>
                  <p className="text-slate-800">{selectedNode.joinDate}</p>
                </div>
              )}
              
              {selectedNode.platform && (
                <div>
                  <Label className="text-sm font-medium text-slate-600">Platform</Label>
                  <p className="text-slate-800">{selectedNode.platform}</p>
                </div>
              )}
              
              {selectedNode.status && (
                <div>
                  <Label className="text-sm font-medium text-slate-600">Status</Label>
                  <Badge className={getStatusBadge(selectedNode.status).color}>
                    {selectedNode.status}
                  </Badge>
                </div>
              )}
              
              {selectedNode.date && (
                <div>
                  <Label className="text-sm font-medium text-slate-600">Date</Label>
                  <p className="text-slate-800">{selectedNode.date}</p>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ReferralTree;
