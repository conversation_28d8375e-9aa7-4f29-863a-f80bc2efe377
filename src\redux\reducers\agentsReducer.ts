import { AgentsState, Action } from '../types';
import * as actionTypes from '../constants/actionTypes';

const initialState: AgentsState = {
  agents: [],
  selectedAgent: null,
  loading: false,
  error: null
};

const agentsReducer = (state = initialState, action: Action): AgentsState => {
  switch (action.type) {
    case actionTypes.FETCH_AGENTS_REQUEST:
    case actionTypes.ADD_AGENT_REQUEST:
    case actionTypes.UPDATE_AGENT_REQUEST:
    case actionTypes.DELETE_AGENT_REQUEST:
      return {
        ...state,
        loading: true,
        error: null
      };

    case actionTypes.FETCH_AGENTS_SUCCESS:
      return {
        ...state,
        loading: false,
        agents: action.payload,
        error: null
      };

    case actionTypes.ADD_AGENT_SUCCESS:
      return {
        ...state,
        loading: false,
        agents: [...state.agents, action.payload],
        error: null
      };

    case actionTypes.UPDATE_AGENT_SUCCESS:
      return {
        ...state,
        loading: false,
        agents: state.agents.map(agent =>
          agent.id === action.payload.id ? action.payload : agent
        ),
        selectedAgent: state.selectedAgent?.id === action.payload.id ? action.payload : state.selectedAgent,
        error: null
      };

    case actionTypes.DELETE_AGENT_SUCCESS:
      return {
        ...state,
        loading: false,
        agents: state.agents.filter(agent => agent.id !== action.payload),
        selectedAgent: state.selectedAgent?.id === action.payload ? null : state.selectedAgent,
        error: null
      };

    case actionTypes.SET_SELECTED_AGENT:
      return {
        ...state,
        selectedAgent: action.payload
      };

    case actionTypes.FETCH_AGENTS_FAILURE:
    case actionTypes.ADD_AGENT_FAILURE:
    case actionTypes.UPDATE_AGENT_FAILURE:
    case actionTypes.DELETE_AGENT_FAILURE:
      return {
        ...state,
        loading: false,
        error: action.payload
      };

    default:
      return state;
  }
};

export default agentsReducer;
