export const auth0Config = {
  domain: import.meta.env.VITE_AUTH0_DOMAIN || '',
  clientId: import.meta.env.VITE_AUTH0_CLIENT_ID || '',
  authorizationParams: {
    redirect_uri: import.meta.env.VITE_AUTH0_REDIRECT_URI || window.location.origin,
    audience: import.meta.env.VITE_AUTH0_AUDIENCE,
    scope: 'openid profile email offline_access', 
  },
  cacheLocation: 'localstorage' as const,
  useRefreshTokens: true,
};

export const logoutConfig = {
  logoutParams: {
    returnTo: import.meta.env.VITE_AUTH0_LOGOUT_URI || window.location.origin,
  },
};
