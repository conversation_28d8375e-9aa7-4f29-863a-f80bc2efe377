
import React from "react";
import { toast } from "sonner";
import { CheckCircle, AlertCircle, AlertTriangle, Info, X } from "lucide-react";

interface NotificationToastProps {
  type: "success" | "error" | "warning" | "info";
  title: string;
  description?: string;
  duration?: number;
}

export const showNotification = ({
  type,
  title,
  description,
  duration = 4000,
}: NotificationToastProps) => {
  const getIcon = () => {
    const iconProps = "w-5 h-5";
    switch (type) {
      case "success":
        return <CheckCircle className={`${iconProps} text-green-600`} />;
      case "error":
        return <AlertCircle className={`${iconProps} text-red-600`} />;
      case "warning":
        return <AlertTriangle className={`${iconProps} text-yellow-600`} />;
      default:
        return <Info className={`${iconProps} text-blue-600`} />;
    }
  };

  const toastContent = (
    <div className="flex items-start gap-3 w-full">
      {getIcon()}
      <div className="flex-1 min-w-0">
        <p className="font-semibold text-sm text-foreground">{title}</p>
        {description && (
          <p className="text-sm text-muted-foreground mt-1">{description}</p>
        )}
      </div>
    </div>
  );

  toast.custom(() => (
    <div className="bg-background border border-border rounded-lg shadow-lg p-4 max-w-md">
      {toastContent}
    </div>
  ), {
    duration,
  });
};

export const NotificationToast = {
  success: (title: string, description?: string) =>
    showNotification({ type: "success", title, description }),
  error: (title: string, description?: string) =>
    showNotification({ type: "error", title, description }),
  warning: (title: string, description?: string) =>
    showNotification({ type: "warning", title, description }),
  info: (title: string, description?: string) =>
    showNotification({ type: "info", title, description }),
};
