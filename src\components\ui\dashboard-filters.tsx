
import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import {
  Calendar as CalendarIcon,
  Filter,
  Clock,
  CalendarDays,
  CalendarRange
} from "lucide-react";
import { format, subDays, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from "date-fns";

interface DateRange {
  from: Date;
  to: Date;
}

interface DashboardFiltersProps {
  onFilterChange: (period: string, dateRange?: DateRange) => void;
}

const DashboardFilters = ({ onFilterChange }: DashboardFiltersProps) => {
  const [selectedPeriod, setSelectedPeriod] = useState("weekly");
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);
  const [customDateRange, setCustomDateRange] = useState<DateRange>({
    from: subDays(new Date(), 30),
    to: new Date()
  });

  const periods = [
    { id: "daily", label: "Daily", icon: Clock },
    { id: "weekly", label: "Weekly", icon: CalendarDays },
    { id: "monthly", label: "Monthly", icon: CalendarRange },
    { id: "custom", label: "Custom Range", icon: CalendarIcon },
  ];

  const handlePeriodChange = (periodId: string) => {
    setSelectedPeriod(periodId);
    
    let dateRange: DateRange;
    const now = new Date();
    
    switch (periodId) {
      case "daily":
        dateRange = { from: now, to: now };
        break;
      case "weekly":
        dateRange = { from: startOfWeek(now), to: endOfWeek(now) };
        break;
      case "monthly":
        dateRange = { from: startOfMonth(now), to: endOfMonth(now) };
        break;
      case "custom":
        dateRange = customDateRange;
        break;
      default:
        dateRange = { from: startOfWeek(now), to: endOfWeek(now) };
    }
    
    onFilterChange(periodId, dateRange);
  };

  const handleCustomDateChange = (range: DateRange) => {
    setCustomDateRange(range);
    if (selectedPeriod === "custom") {
      onFilterChange("custom", range);
    }
  };

  return (
    <Card className="bg-white/70 backdrop-blur-sm border-border/50 shadow-lg">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Filter className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm font-medium text-foreground">Time Period:</span>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Period Buttons */}
            <div className="flex items-center space-x-1 bg-muted/30 rounded-lg p-1">
              {periods.map((period) => {
                const Icon = period.icon;
                const isActive = selectedPeriod === period.id;
                
                if (period.id === "custom") {
                  return (
                    <Popover key={period.id} open={isDatePickerOpen} onOpenChange={setIsDatePickerOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className={`
                            relative px-3 py-1.5 text-xs font-medium transition-all
                            ${isActive 
                              ? 'bg-primary text-primary-foreground shadow-sm' 
                              : 'hover:bg-muted/70 text-muted-foreground hover:text-foreground'
                            }
                          `}
                          onClick={() => handlePeriodChange(period.id)}
                        >
                          <Icon className="w-3 h-3 mr-1.5" />
                          {period.label}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="end">
                        <div className="p-4 space-y-4">
                          <div className="space-y-2">
                            <h4 className="font-medium text-sm">Select Date Range</h4>
                            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                              <span>From: {format(customDateRange.from, "MMM dd, yyyy")}</span>
                              <span>•</span>
                              <span>To: {format(customDateRange.to, "MMM dd, yyyy")}</span>
                            </div>
                          </div>
                          <Calendar
                            mode="range"
                            selected={{
                              from: customDateRange.from,
                              to: customDateRange.to
                            }}
                            onSelect={(range) => {
                              if (range?.from && range?.to) {
                                handleCustomDateChange({ from: range.from, to: range.to });
                              }
                            }}
                            className="rounded-md border"
                          />
                          <Button
                            size="sm"
                            className="w-full"
                            onClick={() => {
                              setIsDatePickerOpen(false);
                              handlePeriodChange("custom");
                            }}
                          >
                            Apply Range
                          </Button>
                        </div>
                      </PopoverContent>
                    </Popover>
                  );
                }
                
                return (
                  <Button
                    key={period.id}
                    variant="ghost"
                    size="sm"
                    className={`
                      relative px-3 py-1.5 text-xs font-medium transition-all
                      ${isActive 
                        ? 'bg-primary text-primary-foreground shadow-sm' 
                        : 'hover:bg-muted/70 text-muted-foreground hover:text-foreground'
                      }
                    `}
                    onClick={() => handlePeriodChange(period.id)}
                  >
                    <Icon className="w-3 h-3 mr-1.5" />
                    {period.label}
                  </Button>
                );
              })}
            </div>

            {/* Active Filter Badge */}
            {selectedPeriod && (
              <Badge variant="secondary" className="text-xs bg-primary/10 text-primary border-primary/20">
                {periods.find(p => p.id === selectedPeriod)?.label}
                {selectedPeriod === "custom" && (
                  <span className="ml-1">
                    ({format(customDateRange.from, "MMM dd")} - {format(customDateRange.to, "MMM dd")})
                  </span>
                )}
              </Badge>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default DashboardFilters;
