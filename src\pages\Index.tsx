import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Building2,
  Users,
  UserCheck,
  Share2,
  Bell,
  Gift,
  BarChart3,
  GitBranch,
  Settings,
  Plus,
  Download,
  Menu,
  X,
  Phone,
} from "lucide-react";
import { ConfirmationModal } from "@/components/ui/confirmation-modal";
import { NotificationToast } from "@/components/ui/notification-toast";
import { useAuth0Extended } from "@/hooks/useAuth0Extended";
import { logoutConfig } from "@/lib/auth0-config";
import ProtectedRoute from "@/components/ProtectedRoute";
import RoleBasedAccess, {
  SuperAdminOnly,
  AdminOnly,
} from "@/components/RoleBasedAccess";
import UserProfile from "@/components/UserProfile";
import AgentManager from "@/components/AgentManager";
import GuestManager from "@/components/GuestManager";
import ReferralTracker from "@/components/ReferralTracker";
import ContactsManager from "@/components/ContactsManager";
import PushNotifications from "@/components/PushNotifications";
import RewardsSystem from "@/components/RewardsSystem";
import AnalyticsDashboard from "@/components/AnalyticsDashboard";
import ReferralTree from "@/components/ReferralTree";
import IntegrationsSettings from "@/components/IntegrationsSettings";
import DashboardOverview from "@/components/DashboardOverview";

// Redux imports
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { setActiveTab, toggleSidebar } from "@/redux/actions/uiActions";
import { fetchNotifications } from "@/redux/actions/notificationActions";

const Index = () => {
  // Redux state and dispatch
  const dispatch = useAppDispatch();
  const { activeTab, isSidebarOpen } = useAppSelector((state) => state.ui);
  const { unreadCount } = useAppSelector((state) => state.notifications);

  const { user, logout } = useAuth0Extended();

  // Initialize data on component mount
  useEffect(() => {
    dispatch(fetchNotifications() as any);
  }, [dispatch]);
  const [confirmModal, setConfirmModal] = useState<{
    isOpen: boolean;
    title: string;
    description: string;
    onConfirm: () => void;
    type?: "danger" | "warning" | "success" | "info";
  }>({
    isOpen: false,
    title: "",
    description: "",
    onConfirm: () => {},
  });

  const handleLogout = () => {
    setConfirmModal({
      isOpen: true,
      title: "Sign Out",
      description: "Are you sure you want to sign out of your account?",
      type: "warning",
      onConfirm: () => {
        logout(logoutConfig);
        NotificationToast.info(
          "Signed Out",
          "You have been successfully signed out"
        );
        setConfirmModal((prev) => ({ ...prev, isOpen: false }));
      },
    });
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case "dashboard":
        return <DashboardOverview />;
      case "agents":
        return (
          <AdminOnly>
            <AgentManager />
          </AdminOnly>
        );
      case "guests":
        return <GuestManager />;
      case "referrals":
        return <ReferralTracker />;
      case "contacts":
        return <ContactsManager />;
      case "notifications":
        return (
          <AdminOnly>
            <PushNotifications />
          </AdminOnly>
        );
      case "rewards":
        return <RewardsSystem />;
      case "analytics":
        return (
          <AdminOnly>
            <AnalyticsDashboard />
          </AdminOnly>
        );
      case "tree":
        return <ReferralTree />;
      case "integrations":
        return (
          <SuperAdminOnly>
            <IntegrationsSettings />
          </SuperAdminOnly>
        );
      case "profile":
        return <UserProfile />;
      default:
        return <DashboardOverview />;
    }
  };

  const getHeaderActions = () => {
    switch (activeTab) {
      case "resorts":
        return (
          <div className="flex gap-2">
            <Button variant="outline" size="sm" className="hidden sm:flex">
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
            <Button
              size="sm"
              onClick={() =>
                NotificationToast.info(
                  "Feature Coming Soon",
                  "Add Resort functionality will be available soon"
                )
              }
            >
              <Plus className="mr-2 h-4 w-4" />
              <span className="hidden sm:inline">Add Resort</span>
              <span className="sm:hidden">Add</span>
            </Button>
          </div>
        );
      case "agents":
        return (
          <div className="flex gap-2">
            <Button variant="outline" size="sm" className="hidden sm:flex">
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
            <Button
              size="sm"
              onClick={() =>
                NotificationToast.info(
                  "Feature Coming Soon",
                  "Add Sales Representativefunctionality will be available soon"
                )
              }
            >
              <Plus className="mr-2 h-4 w-4" />
              <span className="hidden sm:inline">Add Agent</span>
              <span className="sm:hidden">Add</span>
            </Button>
          </div>
        );
      default:
        return null;
    }
  };

  const { isSuperAdmin, isResortAdmin } = useAuth0Extended();

  const navigationItems = [
    { id: "dashboard", label: "Dashboard", icon: BarChart3 },
    { id: "agents", label: "Sales Team", icon: Users, adminOnly: true },
    { id: "referrals", label: "Referrals", icon: Share2 },
    { id: "rewards", label: "Rewards", icon: Gift },
    { id: "analytics", label: "Analytics", icon: BarChart3, adminOnly: true },
    { id: "tree", label: "Referral Tree", icon: GitBranch },
    // { id: "guests", label: "Guests", icon: UserCheck },
    { id: "contacts", label: "Contacts", icon: Phone },
    {
      id: "notifications",
      label: "Notifications",
      icon: Bell,
      adminOnly: true,
    },
    // { id: "resorts", label: "Resorts", icon: Building2, superAdminOnly: true },
    {
      id: "integrations",
      label: "Settings",
      icon: Settings,
      superAdminOnly: true,
    },
    { id: "profile", label: "Profile", icon: UserCheck },
  ].filter((item: any) => {
    if (item.superAdminOnly && !isSuperAdmin()) return false;
    if (item.adminOnly && !isSuperAdmin() && !isResortAdmin()) return false;
    return true;
  });

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gradient-to-br from-background via-primary/5 to-secondary/5 font-gotham">
        {/* Fixed Header */}
        <header className="fixed top-0 left-0 right-0 z-50 h-16 bg-white/80 backdrop-blur-xl border-b border-border/50 shadow-sm">
          <div className="flex h-full items-center justify-between px-4 sm:px-6">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-primary to-secondary p-1 rounded-lg flex items-center justify-center">
                <img src="images/vv-logo.png" className="" />
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                VIDA
              </h1>
            </div>

            <div className="flex items-center gap-2 sm:gap-4">
              {/* {getHeaderActions()} */}

              {/* User Info */}
              <div className="hidden sm:flex items-center gap-2 text-sm">
                <span className="text-muted-foreground">Welcome,</span>
                <span className="font-medium">{user?.name || user?.email}</span>
                {user?.role && (
                  <Badge variant="secondary" className="text-xs">
                    {user.role.replace("_", " ").toUpperCase()}
                  </Badge>
                )}
              </div>

              <Button variant="ghost" size="icon" className="relative">
                <Bell className="h-4 w-4" />
                {unreadCount > 0 && (
                  <Badge className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs">
                    {unreadCount}
                  </Badge>
                )}
              </Button>

              <Button
                variant="ghost"
                size="icon"
                className="lg:hidden"
                onClick={() => dispatch(toggleSidebar())}
              >
                {isSidebarOpen ? (
                  <X className="h-4 w-4" />
                ) : (
                  <Menu className="h-4 w-4" />
                )}
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={handleLogout}
                className="hidden sm:flex"
              >
                Sign Out
              </Button>
            </div>
          </div>
        </header>

        <div className="flex pt-16">
          {/* Mobile Sidebar Overlay */}
          {isSidebarOpen && (
            <div
              className="fixed inset-0 bg-black/50 z-40 lg:hidden"
              onClick={() => dispatch(toggleSidebar())}
            />
          )}

          {/* Fixed Sidebar */}
          <aside
            className={`
          fixed lg:static inset-y-16 left-0 z-50 w-64 
          bg-white/80 backdrop-blur-xl border-r border-border/50 shadow-xl
          transform transition-transform duration-200 ease-in-out lg:translate-x-0
          ${isSidebarOpen ? "translate-x-0" : "-translate-x-full"}
          h-[calc(100vh-4rem)]
        `}
          >
            <nav className="p-6 space-y-2 h-full overflow-y-auto">
              <div className="space-y-2">
                {navigationItems.map((item) => {
                  const Icon = item.icon;
                  const isActive = activeTab === item.id;

                  return (
                    <button
                      key={item.id}
                      onClick={() => {
                        dispatch(setActiveTab(item.id));
                        if (isSidebarOpen) {
                          dispatch(toggleSidebar());
                        }
                      }}
                      className={`
                      w-full flex items-center gap-3 px-4 py-3 rounded-xl text-left
                      transition-all duration-200 font-medium group
                      ${
                        isActive
                          ? "bg-gradient-to-r from-primary to-secondary text-white shadow-lg transform scale-[1.02]"
                          : "hover:bg-muted/70 text-muted-foreground hover:text-foreground hover:transform hover:scale-[1.01]"
                      }
                    `}
                    >
                      <Icon
                        className={`h-5 w-5 flex-shrink-0 transition-transform group-hover:scale-110 ${
                          isActive ? "text-white" : ""
                        }`}
                      />
                      <span className="truncate">{item.label}</span>
                    </button>
                  );
                })}
              </div>
            </nav>
          </aside>

          {/* Scrollable Main Content */}
          <main className="flex-1 min-w-0 lg:ml-12">
            <div className="scrollable-content p-6">{renderTabContent()}</div>
          </main>
        </div>

        {/* Confirmation Modal */}
        <ConfirmationModal
          isOpen={confirmModal.isOpen}
          onClose={() =>
            setConfirmModal((prev) => ({ ...prev, isOpen: false }))
          }
          onConfirm={confirmModal.onConfirm}
          title={confirmModal.title}
          description={confirmModal.description}
          type={confirmModal.type}
        />
      </div>
    </ProtectedRoute>
  );
};

export default Index;
