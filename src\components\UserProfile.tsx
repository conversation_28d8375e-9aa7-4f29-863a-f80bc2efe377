import { useAuth0Extended } from '@/hooks/useAuth0Extended';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { 
  User, 
  Mail, 
  Shield, 
  Calendar, 
  MapPin, 
  Phone,
  Building2,
  Eye,
  Settings
} from 'lucide-react';
import { logoutConfig } from '@/lib/auth0-config';

const UserProfile = () => {
  const { user, logout, isSuperAdmin, isResortAdmin, isViewer } = useAuth0Extended();

  if (!user) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-center text-muted-foreground">No user information available</p>
        </CardContent>
      </Card>
    );
  }

  const getRoleInfo = () => {
    if (isSuperAdmin()) {
      return {
        label: 'Super Administrator',
        description: 'Full system access and management',
        color: 'bg-red-100 text-red-700 border-red-200',
        icon: Shield
      };
    } else if (isResortAdmin()) {
      return {
        label: 'Resort Administrator',
        description: 'Resort management and operations',
        color: 'bg-blue-100 text-blue-700 border-blue-200',
        icon: Building2
      };
    } else if (isViewer()) {
      return {
        label: 'Sales Representative',
        description: 'Sales and customer management',
        color: 'bg-gray-100 text-gray-700 border-gray-200',
        icon: Eye
      };
    }
    return {
      label: 'User',
      description: 'Basic access',
      color: 'bg-gray-100 text-gray-700 border-gray-200',
      icon: User
    };
  };

  const roleInfo = getRoleInfo();
  const RoleIcon = roleInfo.icon;

  const getInitials = (name?: string, email?: string) => {
    if (name) {
      return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
    }
    if (email) {
      return email.slice(0, 2).toUpperCase();
    }
    return 'U';
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            User Profile
          </CardTitle>
          <CardDescription>
            Your account information and role details
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Profile Header */}
          <div className="flex items-center space-x-4">
            <Avatar className="h-16 w-16">
              <AvatarImage src={user.picture} alt={user.name || 'User'} />
              <AvatarFallback className="text-lg font-semibold">
                {getInitials(user.name, user.email)}
              </AvatarFallback>
            </Avatar>
            <div className="space-y-1">
              <h3 className="text-xl font-semibold">{user.name || 'Unknown User'}</h3>
              <p className="text-muted-foreground">{user.email}</p>
              <Badge className={`${roleInfo.color} text-xs font-medium`}>
                <RoleIcon className="w-3 h-3 mr-1" />
                {roleInfo.label}
              </Badge>
            </div>
          </div>

          <Separator />

          {/* User Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Email</p>
                  <p className="text-sm text-muted-foreground">{user.email}</p>
                </div>
              </div>

              {user.phone_number && (
                <div className="flex items-center space-x-3">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Phone</p>
                    <p className="text-sm text-muted-foreground">{user.phone_number}</p>
                  </div>
                </div>
              )}

              {user.locale && (
                <div className="flex items-center space-x-3">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Locale</p>
                    <p className="text-sm text-muted-foreground">{user.locale}</p>
                  </div>
                </div>
              )}
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <Shield className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Role</p>
                  <p className="text-sm text-muted-foreground">{roleInfo.description}</p>
                </div>
              </div>

              {user.updated_at && (
                <div className="flex items-center space-x-3">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Last Updated</p>
                    <p className="text-sm text-muted-foreground">
                      {new Date(user.updated_at).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              )}

              {user.email_verified !== undefined && (
                <div className="flex items-center space-x-3">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Email Status</p>
                    <Badge variant={user.email_verified ? "default" : "destructive"} className="text-xs">
                      {user.email_verified ? 'Verified' : 'Not Verified'}
                    </Badge>
                  </div>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Permissions */}
          {user.permissions && user.permissions.length > 0 && (
            <div>
              <h4 className="text-sm font-medium mb-2">Permissions</h4>
              <div className="flex flex-wrap gap-2">
                {user.permissions.map((permission, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {permission}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex gap-2 pt-4">
            <Button
              variant="outline"
              onClick={() => logout(logoutConfig)}
              className="flex items-center gap-2"
            >
              <Settings className="h-4 w-4" />
              Sign Out
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default UserProfile;
