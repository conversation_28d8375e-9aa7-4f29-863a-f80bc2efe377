import { Dispatch } from 'redux';
import { Notification, Action } from '../types';
import * as actionTypes from '../constants/actionTypes';

// Dummy data
const dummyNotifications: Notification[] = [
  {
    id: '1',
    title: 'New Referral Completed',
    message: '<PERSON> completed a referral for <PERSON>',
    type: 'success',
    isRead: false,
    userId: 'user1',
    actionUrl: '/referrals/1',
    priority: 'high',
    createdAt: '2024-01-20T10:30:00Z',
    updatedAt: '2024-01-20T10:30:00Z'
  },
  {
    id: '2',
    title: 'Commission Payment Processed',
    message: 'Your commission of $150 has been processed',
    type: 'info',
    isRead: false,
    userId: 'user1',
    actionUrl: '/rewards',
    priority: 'medium',
    createdAt: '2024-01-20T09:15:00Z',
    updatedAt: '2024-01-20T09:15:00Z'
  },
  {
    id: '3',
    title: 'System Maintenance',
    message: 'Scheduled maintenance will occur tonight from 2-4 AM',
    type: 'warning',
    isRead: true,
    userId: 'user1',
    priority: 'low',
    createdAt: '2024-01-19T16:45:00Z',
    updatedAt: '2024-01-19T17:00:00Z'
  },
  {
    id: '4',
    title: 'New Agent Joined',
    message: '<PERSON> <PERSON> has joined your team',
    type: 'info',
    isRead: false,
    userId: 'user1',
    actionUrl: '/agents/4',
    priority: 'medium',
    createdAt: '2024-01-19T14:20:00Z',
    updatedAt: '2024-01-19T14:20:00Z'
  }
];

// Action Creators
export const fetchNotificationsRequest = (): Action => ({
  type: actionTypes.FETCH_NOTIFICATIONS_REQUEST
});

export const fetchNotificationsSuccess = (notifications: Notification[]): Action => ({
  type: actionTypes.FETCH_NOTIFICATIONS_SUCCESS,
  payload: notifications
});

export const fetchNotificationsFailure = (error: string): Action => ({
  type: actionTypes.FETCH_NOTIFICATIONS_FAILURE,
  payload: error
});

export const addNotificationRequest = (): Action => ({
  type: actionTypes.ADD_NOTIFICATION_REQUEST
});

export const addNotificationSuccess = (notification: Notification): Action => ({
  type: actionTypes.ADD_NOTIFICATION_SUCCESS,
  payload: notification
});

export const addNotificationFailure = (error: string): Action => ({
  type: actionTypes.ADD_NOTIFICATION_FAILURE,
  payload: error
});

export const markNotificationRead = (notificationId: string): Action => ({
  type: actionTypes.MARK_NOTIFICATION_READ,
  payload: notificationId
});

export const deleteNotificationRequest = (): Action => ({
  type: actionTypes.DELETE_NOTIFICATION_REQUEST
});

export const deleteNotificationSuccess = (notificationId: string): Action => ({
  type: actionTypes.DELETE_NOTIFICATION_SUCCESS,
  payload: notificationId
});

export const deleteNotificationFailure = (error: string): Action => ({
  type: actionTypes.DELETE_NOTIFICATION_FAILURE,
  payload: error
});

// Thunk Actions
export const fetchNotifications = () => {
  return (dispatch: Dispatch) => {
    dispatch(fetchNotificationsRequest());
    
    // Simulate API call
    setTimeout(() => {
      try {
        dispatch(fetchNotificationsSuccess(dummyNotifications));
      } catch (error) {
        dispatch(fetchNotificationsFailure('Failed to fetch notifications'));
      }
    }, 800);
  };
};

export const addNotification = (notificationData: Partial<Notification>) => {
  return (dispatch: Dispatch) => {
    dispatch(addNotificationRequest());
    
    // Simulate API call
    setTimeout(() => {
      try {
        const newNotification: Notification = {
          id: Date.now().toString(),
          title: notificationData.title || '',
          message: notificationData.message || '',
          type: notificationData.type || 'info',
          isRead: false,
          userId: notificationData.userId || 'user1',
          actionUrl: notificationData.actionUrl,
          priority: notificationData.priority || 'medium',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        dispatch(addNotificationSuccess(newNotification));
      } catch (error) {
        dispatch(addNotificationFailure('Failed to add notification'));
      }
    }, 500);
  };
};

export const markAsRead = (notificationId: string) => {
  return (dispatch: Dispatch) => {
    // Simulate API call
    setTimeout(() => {
      dispatch(markNotificationRead(notificationId));
    }, 200);
  };
};

export const deleteNotification = (notificationId: string) => {
  return (dispatch: Dispatch) => {
    dispatch(deleteNotificationRequest());
    
    // Simulate API call
    setTimeout(() => {
      try {
        dispatch(deleteNotificationSuccess(notificationId));
      } catch (error) {
        dispatch(deleteNotificationFailure('Failed to delete notification'));
      }
    }, 500);
  };
};
