
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  <PERSON><PERSON>,
  <PERSON>bsContent,
  <PERSON>bs<PERSON>ist,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Settings,
  Webhook,
  MessageSquare,
  Phone,
  Mail,
  TestTube,
  Save,
  RefreshCw,
  CheckCircle,
  XCircle,
  Clock,
  ExternalLink,
  Key,
  Zap
} from "lucide-react";
import { toast } from "sonner";

const IntegrationsSettings = () => {
  const [isTestingWebhook, setIsTestingWebhook] = useState(false);
  const [webhookUrl, setWebhookUrl] = useState("https://api.leadconnectorhq.com/v1/webhooks/your-webhook-id");
  const [apiKey, setApiKey] = useState("ghl_xxxxxxxxxxxxxxxxxxxx");

  const webhookLogs = [
    {
      id: "WH-001",
      timestamp: "2024-07-08 14:30:25",
      event: "referral.completed",
      status: "Success",
      payload: '{"guest_id": "guest001", "referral_id": "REF-001", "contact": {...}}',
      response: "200 OK",
      retry: false
    },
    {
      id: "WH-002", 
      timestamp: "2024-07-08 14:25:18",
      event: "referral.clicked",
      status: "Success", 
      payload: '{"guest_id": "guest002", "referral_id": "REF-002", "clicked_at": "..."}',
      response: "200 OK",
      retry: false
    },
    {
      id: "WH-003",
      timestamp: "2024-07-08 14:20:45",
      event: "referral.sent",
      status: "Failed",
      payload: '{"guest_id": "guest003", "referral_id": "REF-003", "platform": "WhatsApp"}',
      response: "500 Internal Server Error",
      retry: true
    },
    {
      id: "WH-004",
      timestamp: "2024-07-08 14:15:32",
      event: "referral.completed",
      status: "Pending",
      payload: '{"guest_id": "guest004", "referral_id": "REF-004", "contact": {...}}',
      response: "Waiting...",
      retry: false
    }
  ];

  const handleTestWebhook = async () => {
    setIsTestingWebhook(true);
    
    // Simulate webhook test
    setTimeout(() => {
      setIsTestingWebhook(false);
      toast.success("Webhook test successful! Check the logs below.");
    }, 2000);
  };

  const handleSaveSettings = () => {
    toast.success("Integration settings saved successfully!");
  };

  const retryWebhook = (webhookId) => {
    toast.success(`Retrying webhook ${webhookId}...`);
  };

  const getStatusBadge = (status) => {
    const config = {
      "Success": { color: "bg-green-100 text-green-700 border-green-200", icon: CheckCircle },
      "Failed": { color: "bg-red-100 text-red-700 border-red-200", icon: XCircle },
      "Pending": { color: "bg-yellow-100 text-yellow-700 border-yellow-200", icon: Clock }
    };
    return config[status] || config["Pending"];
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-slate-800">Integrations & Settings</h2>
          <p className="text-slate-600 mt-1">Configure third-party integrations and API connections</p>
        </div>
        <Button onClick={handleSaveSettings} className="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600">
          <Save className="w-4 h-4 mr-2" />
          Save All Settings
        </Button>
      </div>

      <Tabs defaultValue="ghl" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="ghl" className="flex items-center space-x-2">
            <Webhook className="w-4 h-4" />
            <span>GoHighLevel</span>
          </TabsTrigger>
          <TabsTrigger value="messaging" className="flex items-center space-x-2">
            <MessageSquare className="w-4 h-4" />
            <span>Messaging</span>
          </TabsTrigger>
          <TabsTrigger value="webhooks" className="flex items-center space-x-2">
            <Zap className="w-4 h-4" />
            <span>Webhook Logs</span>
          </TabsTrigger>
          <TabsTrigger value="api" className="flex items-center space-x-2">
            <Key className="w-4 h-4" />
            <span>API Keys</span>
          </TabsTrigger>
        </TabsList>

        {/* GoHighLevel Integration */}
        <TabsContent value="ghl" className="space-y-6">
          <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Webhook className="w-5 h-5 mr-2 text-blue-600" />
                GoHighLevel CRM Integration
              </CardTitle>
              <CardDescription>
                Configure webhook and API settings to sync referral data with your GHL account
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label htmlFor="webhookUrl">Webhook URL</Label>
                <div className="flex space-x-2">
                  <Input
                    id="webhookUrl"
                    value={webhookUrl}
                    onChange={(e) => setWebhookUrl(e.target.value)}
                    placeholder="https://api.leadconnectorhq.com/v1/webhooks/your-webhook-id"
                    className="flex-1"
                  />
                  <Button
                    variant="outline"
                    onClick={handleTestWebhook}
                    disabled={isTestingWebhook}
                  >
                    {isTestingWebhook ? (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                        Testing...
                      </>
                    ) : (
                      <>
                        <TestTube className="w-4 h-4 mr-2" />
                        Test
                      </>
                    )}
                  </Button>
                </div>
                <p className="text-xs text-slate-500 mt-1">
                  This webhook will receive referral completion events
                </p>
              </div>

              <div>
                <Label htmlFor="ghlApiKey">GHL API Key</Label>
                <Input
                  id="ghlApiKey"
                  type="password"
                  value={apiKey}
                  onChange={(e) => setApiKey(e.target.value)}
                  placeholder="ghl_xxxxxxxxxxxxxxxxxxxx"
                />
                <p className="text-xs text-slate-500 mt-1">
                  Your GoHighLevel API key for authenticating requests
                </p>
              </div>

              <div className="bg-slate-50 p-4 rounded-lg">
                <h4 className="font-semibold text-slate-800 mb-2">Webhook Events</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center justify-between">
                    <span>referral.sent</span>
                    <Badge variant="outline">When a referral is shared</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>referral.clicked</span>
                    <Badge variant="outline">When referral link is clicked</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>referral.completed</span>
                    <Badge variant="outline">When contact form is submitted</Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Messaging Integration */}
        <TabsContent value="messaging" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MessageSquare className="w-5 h-5 mr-2 text-green-600" />
                  WhatsApp API
                </CardTitle>
                <CardDescription>360Dialog or WhatsApp Cloud API</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="whatsappToken">API Token</Label>
                  <Input
                    id="whatsappToken"
                    type="password"
                    placeholder="EAAT..."
                  />
                </div>
                <div>
                  <Label htmlFor="whatsappPhoneId">Phone Number ID</Label>
                  <Input
                    id="whatsappPhoneId"
                    placeholder="123456789012345"
                  />
                </div>
                <Button variant="outline" className="w-full">
                  <TestTube className="w-4 h-4 mr-2" />
                  Test Connection
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Phone className="w-5 h-5 mr-2 text-blue-600" />
                  SMS Provider
                </CardTitle>
                <CardDescription>Twilio or similar SMS service</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="smsAccountSid">Account SID</Label>
                  <Input
                    id="smsAccountSid"
                    type="password"
                    placeholder="ACxxxxxxxxxxxxxxxx"
                  />
                </div>
                <div>
                  <Label htmlFor="smsAuthToken">Auth Token</Label>
                  <Input
                    id="smsAuthToken"
                    type="password"
                    placeholder="xxxxxxxxxxxxxxxx"
                  />
                </div>
                <Button variant="outline" className="w-full">
                  <TestTube className="w-4 h-4 mr-2" />
                  Test Connection
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Mail className="w-5 h-5 mr-2 text-purple-600" />
                  Email Service
                </CardTitle>
                <CardDescription>SMTP or email service provider</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="emailProvider">Provider</Label>
                  <Input
                    id="emailProvider"
                    placeholder="SendGrid, Mailgun, etc."
                  />
                </div>
                <div>
                  <Label htmlFor="emailApiKey">API Key</Label>
                  <Input
                    id="emailApiKey"
                    type="password"
                    placeholder="SG.xxxxxxxxxxxxxxxx"
                  />
                </div>
                <Button variant="outline" className="w-full">
                  <TestTube className="w-4 h-4 mr-2" />
                  Test Connection
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Webhook Logs */}
        <TabsContent value="webhooks" className="space-y-6">
          <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <Zap className="w-5 h-5 mr-2 text-orange-600" />
                  Webhook Activity Log
                </div>
                <Button variant="outline" size="sm">
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Refresh
                </Button>
              </CardTitle>
              <CardDescription>
                Monitor webhook delivery status and debug integration issues
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Timestamp</TableHead>
                    <TableHead>Event</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Response</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {webhookLogs.map((log) => {
                    const statusConfig = getStatusBadge(log.status);
                    const StatusIcon = statusConfig.icon;
                    
                    return (
                      <TableRow key={log.id}>
                        <TableCell>
                          <div className="text-sm">
                            <div>{log.timestamp}</div>
                            <div className="text-xs text-slate-500">{log.id}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="font-mono text-xs">
                            {log.event}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <StatusIcon className="w-4 h-4" />
                            <Badge className={statusConfig.color}>
                              {log.status}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm font-mono">{log.response}</div>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            {log.retry && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => retryWebhook(log.id)}
                              >
                                <RefreshCw className="w-3 h-3" />
                              </Button>
                            )}
                            <Button variant="outline" size="sm">
                              <ExternalLink className="w-3 h-3" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* API Keys */}
        <TabsContent value="api" className="space-y-6">
          <Card className="bg-white/70 backdrop-blur-sm border-slate-200">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Key className="w-5 h-5 mr-2 text-slate-600" />
                API Keys & Secrets
              </CardTitle>
              <CardDescription>
                Manage all API keys and authentication tokens in one place
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <div className="w-5 h-5 text-yellow-500 mt-0.5">⚠️</div>
                  <div>
                    <h4 className="font-semibold text-yellow-800">Security Notice</h4>
                    <p className="text-sm text-yellow-700 mt-1">
                      API keys are encrypted and stored securely. Never share your keys publicly or in unsecured locations.
                    </p>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 gap-4">
                <div className="p-4 border border-slate-200 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold">VIDA API Key</h4>
                    <Badge variant="outline">Internal</Badge>
                  </div>
                  <Input
                    type="password"
                    value="vida_live_xxxxxxxxxxxxxxxxxxxxxxxx"
                    readOnly
                    className="bg-slate-50"
                  />
                  <p className="text-xs text-slate-500 mt-1">Used for internal API authentication</p>
                </div>

                <div className="p-4 border border-slate-200 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold">GoHighLevel</h4>
                    <Badge className="bg-green-100 text-green-700 border-green-200">Connected</Badge>
                  </div>
                  <Input
                    type="password"
                    value={apiKey}
                    onChange={(e) => setApiKey(e.target.value)}
                  />
                  <p className="text-xs text-slate-500 mt-1">CRM integration and lead management</p>
                </div>

                <div className="p-4 border border-slate-200 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold">Push Notifications</h4>
                    <Badge variant="outline">Not Set</Badge>
                  </div>
                  <Input
                    type="password"
                    placeholder="Firebase Server Key"
                  />
                  <p className="text-xs text-slate-500 mt-1">Firebase Cloud Messaging for push notifications</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default IntegrationsSettings;
