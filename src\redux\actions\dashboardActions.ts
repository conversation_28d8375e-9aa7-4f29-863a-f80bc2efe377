import { Dispatch } from 'redux';
import { DashboardStats, ChartData, DashboardFilters, Action } from '../types';
import * as actionTypes from '../constants/actionTypes';

// Dummy data
const dummyDashboardStats: DashboardStats = {
  totalReferrals: 156,
  completedReferrals: 133,
  pendingReferrals: 23,
  totalRevenue: 437000,
  totalCommission: 43700,
  activeAgents: 12,
  activeGuests: 89,
  conversionRate: 85.3
};

const dummyChartData: ChartData[] = [
  { name: 'Mon', referrals: 12, completed: 10, revenue: 15000 },
  { name: 'Tue', referrals: 19, completed: 16, revenue: 22000 },
  { name: 'Wed', referrals: 8, completed: 7, revenue: 9500 },
  { name: 'Thu', referrals: 25, completed: 21, revenue: 31000 },
  { name: 'Fri', referrals: 22, completed: 19, revenue: 28000 },
  { name: 'Sat', referrals: 35, completed: 30, revenue: 42000 },
  { name: '<PERSON>', referrals: 28, completed: 24, revenue: 35000 }
];

const weeklyChartData: ChartData[] = [
  { name: 'Week 1', referrals: 89, completed: 76, revenue: 112000 },
  { name: 'Week 2', referrals: 95, completed: 82, revenue: 125000 },
  { name: 'Week 3', referrals: 78, completed: 68, revenue: 98000 },
  { name: 'Week 4', referrals: 102, completed: 89, revenue: 135000 }
];

const monthlyChartData: ChartData[] = [
  { name: 'Jan', referrals: 364, completed: 315, revenue: 470000 },
  { name: 'Feb', referrals: 298, completed: 256, revenue: 385000 },
  { name: 'Mar', referrals: 412, completed: 358, revenue: 535000 },
  { name: 'Apr', referrals: 387, completed: 334, revenue: 498000 },
  { name: 'May', referrals: 445, completed: 389, revenue: 578000 },
  { name: 'Jun', referrals: 398, completed: 342, revenue: 512000 }
];

// Action Creators
export const fetchDashboardDataRequest = (): Action => ({
  type: actionTypes.FETCH_DASHBOARD_DATA_REQUEST
});

export const fetchDashboardDataSuccess = (data: { stats: DashboardStats; chartData: ChartData[] }): Action => ({
  type: actionTypes.FETCH_DASHBOARD_DATA_SUCCESS,
  payload: data
});

export const fetchDashboardDataFailure = (error: string): Action => ({
  type: actionTypes.FETCH_DASHBOARD_DATA_FAILURE,
  payload: error
});

export const updateDashboardFilters = (filters: DashboardFilters): Action => ({
  type: actionTypes.UPDATE_DASHBOARD_FILTERS,
  payload: filters
});

// Thunk Actions
export const fetchDashboardData = (filters?: DashboardFilters) => {
  return (dispatch: Dispatch) => {
    dispatch(fetchDashboardDataRequest());
    
    // Simulate API call
    setTimeout(() => {
      try {
        let chartData = dummyChartData;
        
        // Adjust data based on filters
        if (filters?.period) {
          switch (filters.period) {
            case 'weekly':
              chartData = weeklyChartData;
              break;
            case 'monthly':
              chartData = monthlyChartData;
              break;
            default:
              chartData = dummyChartData;
          }
        }
        
        // Simulate different stats based on period
        const adjustedStats = {
          ...dummyDashboardStats,
          totalReferrals: filters?.period === 'monthly' ? 2304 : filters?.period === 'weekly' ? 364 : 156,
          completedReferrals: filters?.period === 'monthly' ? 1994 : filters?.period === 'weekly' ? 315 : 133,
          pendingReferrals: filters?.period === 'monthly' ? 310 : filters?.period === 'weekly' ? 49 : 23,
          totalRevenue: filters?.period === 'monthly' ? 2978000 : filters?.period === 'weekly' ? 470000 : 437000,
          totalCommission: filters?.period === 'monthly' ? 297800 : filters?.period === 'weekly' ? 47000 : 43700
        };
        
        dispatch(fetchDashboardDataSuccess({
          stats: adjustedStats,
          chartData
        }));
      } catch (error) {
        dispatch(fetchDashboardDataFailure('Failed to fetch dashboard data'));
      }
    }, 1000);
  };
};

export const updateFilters = (filters: DashboardFilters) => {
  return (dispatch: Dispatch) => {
    dispatch(updateDashboardFilters(filters));
    // Refetch data with new filters
    dispatch(fetchDashboardData(filters));
  };
};
