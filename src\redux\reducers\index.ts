import { combineReducers } from 'redux';
import { RootState } from '../types';

// Import all reducers
import agentsReducer from './agentsReducer';
import dashboardReducer from './dashboardReducer';
import referralsReducer from './referralsReducer';
import notificationsReducer from './notificationsReducer';
import uiReducer from './uiReducer';

// Placeholder reducers for other components
import { GuestsState, ContactsState, RewardsState, AnalyticsState, ReferralTreeState, IntegrationsState, UserProfileState, Action } from '../types';

// Placeholder initial states
const guestsInitialState: GuestsState = {
  guests: [],
  loading: false,
  error: null
};

const contactsInitialState: ContactsState = {
  contacts: [],
  loading: false,
  error: null
};

const rewardsInitialState: RewardsState = {
  rewards: [],
  userPoints: 0,
  claimedRewards: [],
  loading: false,
  error: null
};

const analyticsInitialState: AnalyticsState = {
  data: null,
  filters: {
    period: 'daily',
    dateRange: {
      from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      to: new Date()
    }
  },
  loading: false,
  error: null
};

const referralTreeInitialState: ReferralTreeState = {
  treeData: [],
  expandedNodes: [],
  loading: false,
  error: null
};

const integrationsInitialState: IntegrationsState = {
  integrations: [],
  loading: false,
  error: null
};

const userProfileInitialState: UserProfileState = {
  profile: null,
  loading: false,
  error: null
};

// Placeholder reducers (you can expand these later)
const guestsReducer = (state = guestsInitialState, action: Action): GuestsState => {
  switch (action.type) {
    default:
      return state;
  }
};

const contactsReducer = (state = contactsInitialState, action: Action): ContactsState => {
  switch (action.type) {
    default:
      return state;
  }
};

const rewardsReducer = (state = rewardsInitialState, action: Action): RewardsState => {
  switch (action.type) {
    default:
      return state;
  }
};

const analyticsReducer = (state = analyticsInitialState, action: Action): AnalyticsState => {
  switch (action.type) {
    default:
      return state;
  }
};

const referralTreeReducer = (state = referralTreeInitialState, action: Action): ReferralTreeState => {
  switch (action.type) {
    default:
      return state;
  }
};

const integrationsReducer = (state = integrationsInitialState, action: Action): IntegrationsState => {
  switch (action.type) {
    default:
      return state;
  }
};

const userProfileReducer = (state = userProfileInitialState, action: Action): UserProfileState => {
  switch (action.type) {
    default:
      return state;
  }
};

// Combine all reducers
const rootReducer = combineReducers<RootState>({
  agents: agentsReducer,
  guests: guestsReducer,
  referrals: referralsReducer,
  contacts: contactsReducer,
  notifications: notificationsReducer,
  rewards: rewardsReducer,
  analytics: analyticsReducer,
  dashboard: dashboardReducer,
  referralTree: referralTreeReducer,
  integrations: integrationsReducer,
  userProfile: userProfileReducer,
  ui: uiReducer
});

export default rootReducer;
